<script lang="ts" setup>
// import {
//   getImUserSelector,
//   getOrganization,
//   getSelectedUserList,
//   getSubordinates,
//   getUserInfoList,
// } from '#/api';
import type { Nullable, Recordable } from '@vben/types';

import type { ScrollActionType } from '#/components/Container';
import type { TreeActionType } from '#/components/Tree';

import { computed, nextTick, reactive, ref, unref, watch } from 'vue';

// import { useGlobSetting } from '@/hooks/setting';
import { DeleteOutlined } from '@ant-design/icons-vue';
import { Modal as AModal, Avatar, Button, Form, InputSearch, Select, Tag } from 'ant-design-vue';
import { cloneDeep, pick } from 'lodash-es';

import { ScrollContainer } from '#/components/Container';
import FeEmpty from '#/components/Fe/Empty/src/Empty.vue';
import ModalClose from '#/components/Modal/src/components/ModalClose.vue';
import { BasicTree } from '#/components/Tree';
import { useAttrs } from '#/hooks/core/useAttrs';

import { userSelectProps } from './props';

defineOptions({ name: 'FeUserSelect', inheritAttrs: false });
const props = defineProps(userSelectProps);
const emit = defineEmits(['update:value', 'change', 'labelChange']);
const { getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi } = props.api;
const attrs: any = useAttrs({ excludeDefaultKeys: false });
// const globSetting = useGlobSetting();
// const apiUrl = ref(globSetting.apiUrl);
const visible = ref(false);
const treeRef = ref<Nullable<TreeActionType>>(null);
const innerValue = ref<any[] | string | undefined>([]);
const nodeId = ref('-1');
const treeKey = ref(Date.now());
const pagination = reactive({
  keyword: '',
  currentPage: 1,
  pageSize: 20,
});
const finish = ref<boolean>(false);
const isAsync = ref<boolean>(false);
const activeKey = ref('');
const infiniteBody = ref<Nullable<ScrollActionType>>(null);
const treeData = ref<any[]>([]);
const ableQuery = reactive<Recordable>({ ids: [] });
const sysUserList = ref<any[]>([
  {
    id: 'currentUser',
    fullName: '当前用户',
    headIcon: '/api/file/Image/userAvatar/001.png',
  },
]);
const ableList = ref<any[]>([]);
const options = ref<any[]>([]);
const loading = ref(false);
const selectedData = ref<any[]>([]);
const formItemContext = Form.useInjectFormItemContext();

const getSelectBindValue = computed(() => ({
  ...pick(props, ['placeholder', 'disabled', 'size', 'allowClear']),
  fieldNames: { label: 'fullName', value: 'id' },
  open: false,
  mode: props.multiple ? 'multiple' : '',
  showSearch: false,
  showArrow: true,
  class: unref(attrs).class ? `w-full ${unref(attrs).class}` : 'w-full',
  style: Reflect.has(unref(attrs), 'style') ? unref(attrs).style : {},
}));

watch(
  () => props.value,
  () => {
    setValue();
  },
  { immediate: true },
);
watch(
  () => activeKey.value,
  (val) => {
    if (!val) return;
    pagination.keyword = '';
    nodeId.value = '-1';
    isAsync.value = false;
    initData();
  },
);
watch(
  () => visible.value,
  (val) => {
    if (!val) activeKey.value = '';
  },
);

function setValue() {
  if (!props.value || props.value.length === 0) return setNullValue();
  const ids = props.multiple ? (props.value as any[]) : [props.value];
  if (!Array.isArray(ids)) return;
  const hasSysItem = ids.includes('currentUser');
  getUserInfoByIdsApi(unref(ids)).then((res) => {
    if (!props.value || props.value.length === 0) return setNullValue();
    const selectedList: any[] = res;
    if (hasSysItem) selectedList.push(...sysUserList.value);
    const innerIds = selectedList.map((o) => o.id);
    innerValue.value = props.multiple ? innerIds : innerIds[0];
    options.value = cloneDeep(selectedList);
    selectedData.value = cloneDeep(selectedList);
    const labels = selectedData.value.map((o) => o.fullName).join(',');
    emit('labelChange', labels);
  });
}
function setNullValue() {
  innerValue.value = props.multiple ? [] : undefined;
  options.value = [];
  selectedData.value = [];
  emit('labelChange', '');
}
function onChange(_val, option) {
  selectedData.value = option ?? [];
  handleSubmit();
}
function onTagClose(i) {
  selectedData.value.splice(i, 1);
  handleSubmit();
}
function openSelectModal() {
  if (props.disabled) return;
  visible.value = true;
  isAsync.value = false;
  pagination.keyword = '';
  pagination.currentPage = 1;
  nodeId.value = '0';
  activeKey.value = '1';
  treeData.value = [];
  if (props.selectType === 'all') return setValue();
  if (props.selectType === 'custom') {
    ableQuery.ids = props.ableIds;
  } else {
    const suffix = `--${getAbleKey(props.selectType)}`;
    const ableIds = props.ableRelationIds
      ? Array.isArray(props.ableRelationIds)
        ? props.ableRelationIds
        : [props.ableRelationIds]
      : [];
    ableQuery.ids = ableIds.map((o) => o + suffix);
  }
  ableList.value = [];
  finish.value = false;
  getAbleList();
  nextTick(() => {
    // bindScroll();
    setValue();
  });
}

function getAbleKey(selectType) {
  if (selectType === 'dep') return 'department';
  if (selectType === 'pos') return 'position';
  return selectType;
}
function handleCancel() {
  visible.value = false;
}
function handleSearch() {
  if (loading.value) return;
  treeKey.value = Date.now();
  nodeId.value = '-1';
  treeData.value = [];
  ableList.value = [];
  pagination.currentPage = 1;
  isAsync.value = !!pagination.keyword;
  finish.value = false;
  if (isAsync.value && activeKey.value === '1') {
    nextTick(() => {
      // bindScroll();
    });
  }
  if (props.selectType === 'all') {
    initData();
  } else {
    getAbleList();
  }
}
// function bindScroll() {
//   const bodyRef = infiniteBody.value;
//   const vBody = bodyRef?.getScrollWrap();
//   vBody?.addEventListener('scroll', () => {
//     if (vBody.scrollHeight - vBody.clientHeight - vBody.scrollTop <= 200 && !loading.value && !finish.value) {
//       pagination.currentPage += 1;
//       props.selectType === 'all' ? getAllList() : getAbleList();
//     }
//   });
// }
function handleSelect(keys) {
  if (keys.length === 0) return;
  const data = getTree().getSelectedNode(keys[0]);
  if (data?.type !== 'USER') return;
  handleNodeClick(data);
}
function handleNodeClick(data) {
  const boo = selectedData.value.some((o) => o.id === data.id);
  if (boo) return;
  props.multiple ? selectedData.value.push(data) : (selectedData.value = [data]);
}
function removeAll() {
  selectedData.value = [];
}
function removeData(index: number) {
  selectedData.value.splice(index, 1);
}
function getTree() {
  const tree = unref(treeRef);
  if (!tree) {
    throw new Error('tree is null!');
  }
  return tree;
}
function handleSubmit() {
  const ids = unref(selectedData).map((o) => o.id);
  options.value = unref(selectedData);
  innerValue.value = props.multiple ? ids : ids[0];
  if (props.multiple) {
    emit('update:value', ids);
    emit('change', ids, unref(options));
  } else {
    emit('update:value', ids[0] || '');
    emit('change', ids[0] || '', unref(options)[0]);
  }
  formItemContext.onFieldChange();
  handleCancel();
}
const iconList = {
  COMPANY: 'clarity:organization-line',
  DEPARTMENT: 'clarity:group-line',
  USER: 'lucide:user',
};
const getTreeList = async (params) => {
  if (params.orgId && params.orgId.includes('_')) {
    const idList = params.orgId.split('_');
    params.orgId = idList[idList.length - 1];
  }
  let api = getUserTreeListApi;
  if (params.keyword) {
    api = getUserListByKeywordApi;
    params.keywords = params.keyword.trim();
  }
  const res = await api(params);
  res.forEach((item) => {
    item.isLeaf = Boolean(item.isLeaf);
    item.icon = iconList[item.type];
    if (item.type !== 'USER') {
      item.id = `${item.type}_${item.id}`;
    }
  });
  return res;
};
function onLoadData(node) {
  nodeId.value = node.id;
  return new Promise((resolve: (value?: unknown) => void) => {
    getTreeList({ orgId: nodeId.value }).then((res) => {
      const list = res;
      getTree().updateNodeByKey(node.eventKey, { children: list, isLeaf: list.length === 0 });
      resolve();
    });
  });
}
function getAllList() {
  loading.value = true;
  if (pagination.keyword) nodeId.value = '';
  getTreeList({ orgId: nodeId.value, ...pagination }).then((res) => {
    if (pagination.keyword) {
      if (res.length < pagination.pageSize) finish.value = true;
      treeData.value = [...treeData.value, ...res];
    } else {
      treeData.value = res;
      if (treeData.value.length > 0 && nodeId.value === '-1') {
        getTree().setExpandedKeys([treeData.value[0].id]);
      }
    }
    loading.value = false;
  });
}
function getAbleList() {
  loading.value = true;
  const query = { pagination, ...ableQuery };
  getUserInfoByIdsApi(query)
    .then((res) => {
      if (res.data.list.length < pagination.pageSize) finish.value = true;
      ableList.value = [...ableList.value, ...res.data.list];
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
async function initData() {
  ableList.value = [];
  if (props.selectType === 'all' && activeKey.value === '1') return getAllList();
  // if (activeKey.value === '2') {
  //   loading.value = true;
  //   getOrganization({ keyword: pagination.keyword, organId: '0' }).then((res) => {
  //     ableList.value = res.data;
  //     loading.value = false;
  //   });
  // }
  // if (activeKey.value === '3') {
  //   loading.value = true;
  //   getSubordinates(pagination.keyword).then((res) => {
  //     ableList.value = res.data;
  //     loading.value = false;
  //   });
  // }
  // if (activeKey.value === '4') {
  //   ableList.value = cloneDeep(sysUserList.value);
  // }
}
</script>

<template>
  <div class="select-tag-list" :class="[$attrs.class]" v-if="buttonType === 'button'">
    <Button pre-icon="icon-ym icon-ym-btn-add" @click="openSelectModal">{{ modalTitle }}</Button>
    <div class="tags">
      <Tag class="!mt-10px" :closable="!disabled" v-for="(item, i) in options" :key="item.id" @close="onTagClose(i)">
        {{ item.fullName }}
      </Tag>
    </div>
  </div>
  <Select
    v-bind="getSelectBindValue"
    v-model:value="innerValue"
    :options="options"
    @change="onChange"
    @click="openSelectModal"
    v-else
  />
  <AModal
    v-model:open="visible"
    :title="modalTitle"
    :width="800"
    class="transfer-modal"
    @ok="handleSubmit"
    centered
    :mask-closable="false"
    :keyboard="false"
  >
    <template #closeIcon>
      <ModalClose :can-fullscreen="false" @cancel="handleCancel" />
    </template>
    <div class="transfer__body">
      <div class="transfer-pane left-pane">
        <div class="transfer-pane__tool">
          <InputSearch placeholder="请输入姓名" allow-clear v-model:value="pagination.keyword" @search="handleSearch" />
        </div>
        <div class="transfer-pane__body transfer-pane__body-tab" v-if="selectType === 'all'">
          <!--<a-tabs v-model:activeKey="activeKey" :tabBarGutter="10" size="small" class="pane-tabs">-->
          <!--  <a-tab-pane key="1" tab="全部数据"></a-tab-pane>-->
          <!--  <a-tab-pane key="2" tab="当前组织"></a-tab-pane>-->
          <!--  <a-tab-pane key="3" tab="我的下属"></a-tab-pane>-->
          <!--  <a-tab-pane key="4" tab="系统变量" v-if="hasSys"></a-tab-pane>-->
          <!--</a-tabs>-->
          <template v-if="activeKey === '1'">
            <BasicTree
              class="tree-main"
              :tree-data="treeData"
              :load-data="onLoadData"
              click-row-to-expand
              @select="handleSelect"
              ref="treeRef"
              :key="treeKey"
              :loading="loading"
              :field-names="{ title: 'fullName' }"
              v-if="!isAsync"
            />
            <ScrollContainer v-loading="loading && pagination.currentPage === 1" v-else ref="infiniteBody">
              <div
                v-for="item in treeData"
                :key="item.id"
                class="selected-item selected-item-user"
                @click="handleNodeClick(item)"
              >
                <div class="selected-item-main">
                  <Avatar :size="36" :src="item.headIcon" class="selected-item-headIcon" />
                  <div class="selected-item-text">
                    <p class="name">{{ item.fullName }}</p>
                    <p class="organize" :title="item.organ">{{ item.organ }}</p>
                  </div>
                </div>
              </div>
              <FeEmpty v-if="treeData.length === 0" />
            </ScrollContainer>
          </template>
          <!--<ScrollContainer v-loading="loading" v-else>-->
          <!--  <div-->
          <!--    v-for="item in ableList"-->
          <!--    :key="item.id"-->
          <!--    class="selected-item selected-item-user"-->
          <!--    @click="handleNodeClick(item)"-->
          <!--  >-->
          <!--    <div class="selected-item-main">-->
          <!--      &lt;!&ndash;<a-avatar :size="36" :src="apiUrl + item.headIcon" class="selected-item-headIcon" />&ndash;&gt;-->
          <!--      <div class="selected-item-text">-->
          <!--        <p class="name">{{ item.name }}</p>-->
          <!--        <p class="organize" :title="item.organNames?.join('，')">{{ item.organNames?.join('，') }}</p>-->
          <!--      </div>-->
          <!--    </div>-->
          <!--  </div>-->
          <!--  <FeEmpty v-if="ableList.length === 0" />-->
          <!--</ScrollContainer>-->
        </div>
        <div class="transfer-pane__body transfer-pane__body-tab" v-else>
          <div class="custom-title">全部数据</div>
          <ScrollContainer v-loading="loading && pagination.currentPage === 1" ref="infiniteBody">
            <div
              v-for="item in ableList"
              :key="item.id"
              class="selected-item selected-item-user"
              @click="handleNodeClick(item)"
            >
              <div class="selected-item-main">
                <!--<a-avatar :size="36" :src="apiUrl + item.headIcon" class="selected-item-headIcon" />-->
                <div class="selected-item-text">
                  <p class="name">{{ item.name }}</p>
                  <p class="organize" :title="item.organNames?.join('，')">{{ item.organNames?.join('，') }}</p>
                </div>
              </div>
            </div>
            <FeEmpty v-if="ableList.length === 0" />
          </ScrollContainer>
        </div>
      </div>
      <div class="transfer-pane right-pane">
        <div class="transfer-pane__tool">
          <span>已选</span>
          <span class="remove-all-btn" @click="removeAll">清空列表</span>
        </div>
        <div class="transfer-pane__body">
          <ScrollContainer>
            <div v-for="(item, i) in selectedData" :key="i" class="selected-item selected-item-user">
              <div class="selected-item-main">
                <Avatar :size="36" :src="item.headIcon" class="selected-item-headIcon" />
                <div class="selected-item-text">
                  <p class="name">{{ item.fullName }}</p>
                  <p class="organize" :title="item.organ">{{ item.organ }}</p>
                </div>
                <DeleteOutlined class="delete-btn" @click="removeData(i)" />
              </div>
            </div>
            <FeEmpty v-if="selectedData.length === 0" />
          </ScrollContainer>
        </div>
      </div>
    </div>
  </AModal>
</template>
