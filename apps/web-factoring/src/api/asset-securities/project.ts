// ABS项目信息接口扩展
import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface AbsProjectInfo extends BaseDataParams {
  // ABS项目编号
  projectCode: string;
  // ABS项目名称
  projectName: string;
  // 原始受益人
  originalBeneficiaryName: string;
  // 原始受益人统一社会信用代码
  originalBeneficiaryCode: string;
  // 操作状态
  status: string;
  // 审批状态
  reviewStatus: string;
  // 立项批复
  reviewRemark: string;
  // 立项日期
  establishDate: Date;
  // ABS项目简介
  absProjectSummary: string;
  // 法定代表人
  legalRepresentative: string;
  // 注册资本（万元）
  registeredCapitalWan: number;
  // 经营范围
  businessScope: string;
  // 计划管理人
  planManager: string;
  // 资格证书编号
  qualificationCertificateNo: string;
  // 项目负责人
  projectManager: string;
  // 托管银行
  custodianBank: string;
  // 评级机构
  ratingAgency: string;
  // 律师事务所
  lawFirm: string;
  // 优先级/次级比例
  trancheRatio: string;
  // 各层级利率
  trancheInterestRates: string;
  // 信用增级方式
  creditEnhancementMethod: string;
  // 分配顺序
  paymentWaterfall: string;
  // 支付频率
  paymentFrequency: string;
  // 基础资产转让方式
  assetTransferMethod: string;
  // 证券发行方式
  issuanceMethod: string;
  // 法律法规依据
  legalBasis: string;
  // 审批备案情况
  approvalFilingSummary: string;
  // 信用风险评估
  creditRiskSummary: string;
  // 市场风险评估
  marketRiskSummary: string;
  // 操作风险评估
  operationalRiskSummary: string;
  // 是否审批
  reviewFlag: boolean;
}

// 获取ABS项目分页列表
export async function getAbsProjectPageListApi(params: PageListParams) {
  return requestClient.get<AbsProjectInfo[]>('/abs/project/page', { params });
}

// 添加ABS项目
export async function addAbsProjectApi(data: AbsProjectInfo) {
  return requestClient.post<AbsProjectInfo>('/abs/project/add', data);
}

// 编辑ABS项目
export async function editAbsProjectApi(data: AbsProjectInfo) {
  return requestClient.post<AbsProjectInfo>('/abs/project/edit', data);
}

// 获取ABS项目详情
export async function infoAbsProjectApi(data: AbsProjectInfo) {
  return requestClient.post<AbsProjectInfo>('/abs/project/info', data);
}

// 删除ABS项目
export async function delAbsProjectApi(id: string) {
  return requestClient.post('/abs/project/delete', {}, { params: { id } });
}
