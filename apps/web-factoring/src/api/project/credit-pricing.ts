import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface CreditPricingInfo {
  /**
   * 区域特性
   */
  areaCharacteristics?: string;
  /**
   * 底层项目
   */
  bottomProject?: string;
  calculation?: ProjectUseCalculationBO;
  /**
   * 用信定价综合收益率（%/年）
   */
  compositeYieldRate?: number;
  /**
   * 用信主体编码
   */
  creditCompanyCode?: string;
  /**
   * 用信主体名称
   */
  creditCompanyName?: string;
  /**
   * 用信费率（%/年）
   */
  creditRate?: number;
  /**
   * 客户类别
   */
  customerCategory?: string;
  /**
   * 客户分级
   */
  customerLevel?: string;
  /**
   * 拟用信金额（元）
   */
  expectedUseAmount?: number;
  /**
   * 担保方式描述
   */
  guaranteeMethodDesc?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 行业属性
   */
  industryAttributes?: string;
  /**
   * 是否审批（复核）
   */
  isReview?: number;
  /**
   * 提交操作
   */
  isSubmit?: boolean;
  /**
   * 回款方式
   */
  paymentCollectionMethod?: string;
  /**
   * 授信额度（元）
   */
  pricingCreditAmount?: number;
  /**
   * 授信费率（%）
   */
  pricingCreditRate?: number;
  /**
   * 授信期限（月）
   */
  pricingCreditTerm?: number;
  /**
   * 是否循环额度（授信方式）
   */
  pricingCreditType?: string;
  /**
   * 项目用信定价名称
   */
  pricingName?: string;
  /**
   * 其他情况说明
   */
  pricingOtherDesc?: string;
  /**
   * 项目编号
   */
  projectCode?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目类型
   */
  projectType?: string;
  /**
   * 审批状态（复核）
   */
  reviewStatus?: string;
  /**
   * 服务费（元）
   */
  serviceFeeAmount?: number;
  /**
   * 操作状态
   */
  status?: string;
  /**
   * 合作企业Code
   */
  targetCompanyCode?: number;
  /**
   * 合作企业名称
   */
  targetCompanyName?: string;
  [property: string]: any;
}

/**
 * ProjectUseCalculationBO，项目用信还本付息试算
 */
export interface ProjectUseCalculationBO {
  /**
   * 测算综合收益率（%/年）
   */
  compositeYieldRate?: number;
  /**
   * 项目定价综合收益率（%）
   */
  comprehensiveRate?: number;
  /**
   * 试算明细
   */
  detailList?: ProjectUseCalculationDetailBO[];
  /**
   * 预估最后还款日
   */
  expectedDueDate?: Date;
  /**
   * 预估计息天数（天）
   */
  expectedInterestDays?: number;
  /**
   * 预计业务投放日
   */
  expectedLaunchDate?: Date;
  /**
   * 预估还款期数
   */
  expectedRepayPeriods?: number;
  /**
   * 计划融资金额（元）
   */
  financingAmount?: number;
  /**
   * 融资比例（%）
   */
  financingRatio?: number;
  /**
   * 宽限期天数（天）
   */
  gracePeriodDays?: number;
  /**
   * 宽限期费率（%/年）
   */
  gracePeriodRate?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 分期还息频次
   */
  interestPeriod?: string;
  /**
   * 还息方式
   */
  interestRepaymentMethod?: string;
  /**
   * 固定罚息利率（%）
   */
  penaltyInterestRate?: number;
  /**
   * 阶梯罚息JSON：startDays , endDays , rate
   */
  penaltySteps?: string;
  /**
   * 罚息类型
   */
  penaltyType?: string;
  /**
   * 项目用信规划方式
   */
  planningMethod?: string;
  /**
   * 定价方案变更说明
   */
  pricingChangeDesc?: string;
  /**
   * 分期还本频次
   */
  principalPeriod?: string;
  /**
   * 还本方式
   */
  principalRepaymentMethod?: string;
  /**
   * 定价用信试算ID(复核使用)
   */
  projectUseCalculationId?: number;
  /**
   * 项目用信还本付息试算ID
   */
  projectUsePricingId?: number;
  /**
   * 应收账款金额（元）
   */
  receivableAmount?: number;
  /**
   * 备注（定价方案说明）
   */
  remarks?: string;
  /**
   * 默认当期还息日
   */
  repayInterestDay?: string;
  /**
   * 默认当期还本日
   */
  repayPrincipalDay?: string;
  /**
   * 服务费（元）
   */
  serviceFeeAmount?: number;
  /**
   * 项目用信还本付息试算类型
   */
  usePricingType?: string;
  /**
   * 项目用信试算版本
   */
  usePricingVersion?: string;
  [property: string]: any;
}

/**
 * ProjectUseCalculationDetailBO，项目用信还本付息试算明细
 */
export interface ProjectUseCalculationDetailBO {
  /**
   * 业务类型
   */
  businessType?: string;
  /**
   * 当期还本/付息日
   */
  currentDate?: Date;
  /**
   * 应还宽限期利息(元)
   */
  graceInterestAmount?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 应还利息(元)
   */
  interestAmount?: number;
  /**
   * 应还逾期罚息(元)
   */
  overdueInterestAmount?: number;
  /**
   * 应还本金(元)
   */
  principalAmount?: number;
  /**
   * 试算ID
   */
  projectCalculationId?: number;
  /**
   * 还款期数
   */
  repayPeriods?: number;
  /**
   * 应收服务费(元)
   */
  serviceAmount?: number;
  /**
   * 当期净现金流(元)
   */
  totalAmount?: number;
  [property: string]: any;
}

// 获取项目用信分页列表
export async function getCreditPricingPageListApi(params: PageListParams) {
  return requestClient.get<CreditPricingInfo[]>('/factoring/project/credit/pricing/page', { params });
}

// 添加项目用信
export async function addCreditPricingApi(data: CreditPricingInfo) {
  return requestClient.post<CreditPricingInfo>('/factoring/project/credit/pricing/add', data);
}

// 编辑项目用信
export async function editCreditPricingApi(data: CreditPricingInfo) {
  return requestClient.post<CreditPricingInfo>('/factoring/project/credit/pricing/edit', data);
}

// 添加项目用信-复核
export async function recheckCreditPricingApi(data: CreditPricingInfo) {
  return requestClient.post<CreditPricingInfo>('/factoring/project/credit/pricing/recheck/edit', data);
}

// 编辑项目用信-复核-驳回
export async function rejectCreditPricingApi(id: number) {
  return requestClient.post(`/factoring/project/credit/pricing/recheck/reject/${id}`);
}

// 编辑项目用信-变更
export async function changeCreditPricingApi(id: number) {
  return requestClient.post(`/factoring/project/credit/pricing/change/${id}`);
}

// 获取项目用信详情
export async function getCreditPricingInfoApi(id: number) {
  return requestClient.get(`/factoring/project/credit/pricing/detail/${id}`);
}

// 删除项目用信
export async function delCreditPricingApi(id: number) {
  return requestClient.post(`/factoring/project/credit/pricing/delete/${id}`);
}
