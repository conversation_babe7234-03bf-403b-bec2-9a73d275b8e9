import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

// 应收账款分页视图对象
export interface ReceivablePageVO extends BaseDataParams {
  // 业务类型
  bizType?: string;
  // 债权人
  creditorName?: string;
  // 债务人
  debtorName?: string;
  // 主键
  id?: number;
  // 关联项目
  projectName?: string;
  // 应收账款金额
  receivableAmount?: number;
  // 应收账款到期日
  receivableDueDate?: Date;
  // 应收账款名称
  receivableName?: string;
  // 操作状态
  status?: string;
  // 中登网登记状态
  zdStatus?: string;
}

// 发票信息视图对象
export interface ReceivableInvoiceVO {
  // 购买方
  buyerName?: string;
  // 校验码后6位
  checkCode?: string;
  // 主键
  id?: number;
  // 发票代码
  invoiceCode?: string;
  // 开票日期
  invoiceDate?: Date;
  // 发票号码
  invoiceNumber?: string;
  // 发票类型
  invoiceType?: string;
  // 应收账款ID
  receivableId?: number;
  // 销售方
  sellerName?: string;
  // 不含税金额
  totalAmount?: number;
  // 含税金额
  totalAmountTax?: number;
  // 验真结果
  verifyResult?: string;
}

// 合同信息视图对象
export interface ReceivableContractVO {
  // 基础合同编号
  contractCode?: string;
  // 基础合同名称
  contractName?: string;
  // 基础合同类型
  contractType?: string;
  // 主键
  id?: number;
  // 应收账款ID
  receivableId?: number;
  // 合同签署日期
  signedDate?: Date;
  // 合同金额
  totalAmount?: number;
  // 拟转让应收账款金额
  transferAmount?: number;
  // 未付款项金额
  unpaidAmount?: number;
  // 凭证名称
  voucherName?: string;
  // 凭证号码
  voucherNumber?: string;
}

// 应收账款视图对象
export interface ReceivableVO {
  // 所属ABS基础资产ID
  absBasisAssetId?: number;
  // 业务类型
  bizType?: string;
  // 基础合同列表
  contractList?: ReceivableContractVO[];
  // 债权人、债务人关系
  creditorDebtorDel?: string;
  // 债权人
  creditorName?: string;
  // 债务人
  debtorName?: string;
  // 主键
  id?: number;
  // 发票列表
  invoiceList?: ReceivableInvoiceVO[];
  // 应收账款金额
  receivableAmount?: number;
  // 应收账款编号
  receivableCode?: string;
  // 应收账款到期日
  receivableDueDate?: Date;
  // 应收账款名称
  receivableName?: string;
  // 应收账款期限(月)
  receivableTerm?: number;
  // 应收账款说明
  remarks?: string;
  // 操作状态
  status?: string;
  // 关联登记编号
  zdCode?: string;
  // 中登网登记状态
  zdStatus?: string;
}

// 获取应收账款分页列表
export async function getReceivablePageListApi(params: PageListParams) {
  return requestClient.get<ReceivablePageVO[]>('/receivable/page', { params });
}

// 应收账款详情
export async function infoReceivableApi(id: string) {
  return requestClient.get<ReceivableVO>(`/receivable/detail/${id}`);
}

// 新增应收账款
export async function addReceivableApi(data: ReceivableVO) {
  return requestClient.post<string>('/receivable/add', data);
}

// 编辑应收账款
export async function editReceivableApi(data: ReceivableVO) {
  return requestClient.post<string>('/receivable/edit', data);
}

// 删除应收账款
export async function delReceivableApi(id: string) {
  return requestClient.get(`/receivable/delete/${id}`);
}
