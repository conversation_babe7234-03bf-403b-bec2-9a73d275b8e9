<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Button, message, Space, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {} from '#/api';

import Create from './create.vue';

// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '运营分析报告名称',
    },
    {
      component: 'RangePicker',
      fieldName: 'businessDate', // 修复字段名：productName -> businessDate
      label: '分析时段',
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

// 修复表格列显示和数据绑定的bug
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectCode', title: '项目名称' },
    { field: 'projectName', title: '运营分析报告名称' },
    { field: 'businessStructure', title: '分析时段' },
    { field: 'projectModel', title: '分析人' },
    { field: 'supplierCompanyName', title: '分析日期' },
    { field: 'status', title: '操作状态' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getProjectPageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const add = () => {
  openFormPopup(true, {});
};

// 定义正确的类型
interface ProjectItem {
  id: number;
  projectCode: string;
  projectName: string;
  businessStructure: string;
  projectModel: string;
  supplierCompanyName: string;
  purchaserCompanyName: string;
  executorCompanyName: string;
  status: string; // 修复字段名
  approvalStatus: string;
  businessManagerName: string;
  businessDate: string;
  createTime: string;
  createBy: string;
  createDeptName: string;
}

const edit = (row: ProjectItem) => {
  openFormPopup(true, row);
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();

const del = async (_row: ProjectItem) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      try {
        // await projectDeleteApi(row.id); // 恢复删除API调用
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch {
        // message.error('删除失败: ' + error.message);
      }
    },
  });
};
const detail = (_row: ProjectItem) => {};

const exportWord = () => {};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="exportWord(row)"> 导出Word报告 </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
