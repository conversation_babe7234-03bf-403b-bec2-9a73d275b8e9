<script lang="ts" setup>
import type { VxeGridListeners, VxeGridProps } from '#/adapter/vxe-table';

// 导入 ref 和 watchEffect
import { ref, watchEffect } from 'vue';

import { message } from 'ant-design-vue'; // 引入 message

import { useVbenVxeGrid } from '#/adapter/vxe-table';

// 定义 props 接收父组件数据
const props = defineProps<{
  // 动态表头数据，格式为字符串数组
  dynamicHeaders: string[];
  // 表格数据
  tableData: any[];
}>();

// 定义表格列，第一列固定为经营成果（万元）
const columns = ref([{ title: '经营成果（万元）', field: 'result' }]);

// 监听动态表头变化，更新表格列
watchEffect(() => {
  columns.value = [
    { title: '经营成果（万元）', field: 'result' },
    // 检查 dynamicHeaders 是否存在，若不存在则使用空数组
    ...(props.dynamicHeaders || []).map((header: string) => ({
      title: header,
      field: header.replaceAll(/[年月]/g, ''),
    })),
  ];
});

// 表格配置
const gridOptions: VxeGridProps<any> = {
  columns: columns.value,
  data: props.tableData,
  pagerConfig: {
    enabled: false,
  },
  sortConfig: {
    multiple: true,
  },
};

// 表格事件
const gridEvents: VxeGridListeners<any> = {
  cellClick: ({ row }) => {
    // 修正可能的字段错误
    message.info(`cell-click: ${row.result || '无数据'}`);
  },
};

// 使用 Vben VxeGrid
const [Grid] = useVbenVxeGrid({ gridEvents, gridOptions });
</script>

<template>
  <div class="vp-raw w-full">
    <Grid />
  </div>
</template>
