<script setup lang="ts">
import type { InitiationInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import {
  addComprehensiveInitiationApi,
  addSingleInitiationApi,
  editComprehensiveInitiationApi,
  editSingleInitiationApi,
  getInitiationInfoApi,
} from '#/api';
import ComprehensiveEdit from '#/views/project/initiation/components/comprehensive-edit.vue';
import SingleEdit from '#/views/project/initiation/components/single-edit.vue';

const emit = defineEmits(['ok', 'register']);

const initiationForm = ref<InitiationInfo>({});
const ComprehensiveFormRef = ref();
const SingleFormRef = ref();
const init = async (data: InitiationInfo) => {
  initiationForm.value = data?.id ? await getInitiationInfoApi(data.id as number) : data;
  if (data.projectType === 'comprehensive') {
    if (!initiationForm.value?.creditUser) initiationForm.value.creditUser = [];
    ComprehensiveFormRef.value.init(initiationForm.value);
  } else {
    SingleFormRef.value.init(initiationForm.value);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const save = async () => {
  await (initiationForm.value.projectType === 'comprehensive'
    ? ComprehensiveFormRef.value.save()
    : SingleFormRef.value.save());

  const formData = cloneDeep(initiationForm.value);
  changeOkLoading(true);
  let api = formData.projectType === 'comprehensive' ? addComprehensiveInitiationApi : addSingleInitiationApi;
  if (formData.id) {
    api = formData.projectType === 'comprehensive' ? editComprehensiveInitiationApi : editSingleInitiationApi;
  }
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="项目立项" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <ComprehensiveEdit
        v-show="initiationForm.projectType === 'comprehensive'"
        ref="ComprehensiveFormRef"
        v-model="initiationForm"
      />
      <SingleEdit v-show="initiationForm.projectType === 'single'" ref="SingleFormRef" v-model="initiationForm" />
    </div>
  </BasicPopup>
</template>

<style></style>
