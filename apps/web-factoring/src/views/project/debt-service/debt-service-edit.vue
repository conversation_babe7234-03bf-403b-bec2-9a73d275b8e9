<script setup lang="ts">
import type { DebtServiceInfo, InitiationInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { addDebtServiceApi, editDebtServiceApi, getCompanyListApi, getDebtServiceInfoApi } from '#/api';
import LadderPenalty from '#/views/project/components/ladder-penalty.vue';
import RepaymentCalculation from '#/views/project/components/repayment-calculation.vue';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();
const RepaymentCalculationRef = ref();
const LadderPenaltyRef = ref();
const init = async (data: DebtServiceInfo) => {
  debtServiceForm.value = data.id
    ? await getDebtServiceInfoApi(data.id as number)
    : {
        ...data,
      };
  LadderPenaltyRef.value.init();
  RepaymentCalculationRef.value.init();
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const FormRef = ref();
const debtServiceForm = ref<DebtServiceInfo>({});
const save = async () => {
  await FormRef.value.validate();
  await RepaymentCalculationRef.value.save();
  await LadderPenaltyRef.value.save();
  changeOkLoading(true);
  let api = addDebtServiceApi;
  if (debtServiceForm.value.id) {
    api = editDebtServiceApi;
  }
  const formData = cloneDeep(debtServiceForm.value);
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const selectInitiation = (_value: number, data: InitiationInfo) => {
  debtServiceForm.value.pricingName = `${data.projectName}-定价`;
};
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  planningMethod: [{ required: true, message: '请选择还本付息计划规划方式', trigger: 'change' }],
  expectedLaunchDate: [{ required: true, message: '请选择预计业务投放日', trigger: 'change' }],
  expectedDueDate: [{ required: true, message: '请选择预估最后还款日', trigger: 'change' }],
  financingAmount: [{ required: true, message: '请输入计划融资金额' }],
  gracePeriodDays: [{ required: true, message: '请输入宽限期天数' }],
  gracePeriodRate: [{ required: true, message: '请输入宽限期费率' }],
  penaltyInterestRate: [{ required: true, message: '请输入固定罚息利率' }],
  expectedRepayPeriods: [{ required: true, message: '还款期数' }],
  nominalInterestRate: [{ required: true, message: '合同利率' }],
  penaltyType: [{ required: true, message: '请选择罚息类型', trigger: 'change' }],
  principalRepaymentMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  interestRepaymentMethod: [{ required: true, message: '请选择还息方式', trigger: 'change' }],
  principalPeriod: [{ required: true, message: '请选择分期还本频次', trigger: 'change' }],
  interestPeriod: [{ required: true, message: '请选择分期还息频次', trigger: 'change' }],
  repayPrincipalDay: [{ required: true, message: '请选择默认当期还本日', trigger: 'change' }],
  repayInterestDay: [{ required: true, message: '请选择默认当期还息日', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="还本付息计划" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="debtServiceForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联单一项目" name="projectId">
              <ApiComponent
                v-model="debtServiceForm.projectId as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                @change="selectInitiation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="还本付息试算名称" name="calculationName">
              <a-input v-model:value="debtServiceForm.calculationName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="应收账款金额（元）" name="receivableAmount">
              <a-input v-model:value="debtServiceForm.receivableAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="基准定价（%/年）" name="pricingBasicRatio">
              <a-input v-model:value="debtServiceForm.pricingBasicRatio" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="浮动定价（%/年）" name="pricingFloatingRatio">
              <a-input v-model:value="debtServiceForm.pricingFloatingRatio" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目定价综合收益率（%）" name="comprehensiveRate">
              <a-input v-model:value="debtServiceForm.comprehensiveRate" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remarks" v-bind="fullProp">
              <a-textarea v-model:value="debtServiceForm.remarks" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>

        <BasicCaption content="还本付息方案" />

        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="计划融资金额（元）" name="financingAmount">
              <a-input v-model:value="debtServiceForm.financingAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="融资比例（%）" name="financingRatio">
              <a-input v-model:value="debtServiceForm.financingRatio" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期天数（天）" name="gracePeriodDays">
              <a-input v-model:value="debtServiceForm.gracePeriodDays" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期费率（%/年）" name="gracePeriodRate">
              <a-input v-model:value="debtServiceForm.gracePeriodRate" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="合同利率（%/年）" name="nominalInterestRate">
              <a-input v-model:value="debtServiceForm.nominalInterestRate" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="服务费（元）" name="serviceFeeAmount">
              <a-input v-model:value="debtServiceForm.serviceFeeAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="罚息类型" name="penaltyType">
              <a-radio-group
                v-model:value="debtServiceForm.penaltyType"
                :options="dictStore.getDictList('FCT_PENALTY_TYPE')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="debtServiceForm.penaltyType === 'fixed'">
            <a-form-item label="固定罚息利率（%）" name="penaltyInterestRate">
              <a-input v-model:value="debtServiceForm.penaltyInterestRate" />
            </a-form-item>
          </a-col>
        </a-row>
        <LadderPenalty
          ref="LadderPenaltyRef"
          v-show="debtServiceForm.penaltyType === 'ladder'"
          v-model="debtServiceForm"
        />
        <RepaymentCalculation ref="RepaymentCalculationRef" v-model="debtServiceForm" />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
