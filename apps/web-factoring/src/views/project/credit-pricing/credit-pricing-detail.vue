<script setup lang="ts">
import type { CreditPricingInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getCreditPricingInfoApi } from '#/api';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';
import BaseDetail from '#/views/project/credit-pricing/components/base-detail.vue';

defineEmits(['register']); // 保持注册事件，用于弹窗注册
const init = async (data: CreditPricingInfo) => {
  // 仅保留数据获取逻辑，去掉编辑相关的初始化（如表格编辑初始化）
  creditPricingForm.value = data.id ? await getCreditPricingInfoApi(data.id as number) : { ...data };
};
const [registerPopup] = usePopupInner(init);

const creditPricingForm = ref<CreditPricingInfo>({});
</script>

<template>
  <BasicPopup title="还本付息计划详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <BaseDetail :credit-pricing-form="creditPricingForm" />
      <RepaymentCalculationDetail :calculation-form="creditPricingForm" />
    </div>
  </BasicPopup>
</template>

<style></style>
