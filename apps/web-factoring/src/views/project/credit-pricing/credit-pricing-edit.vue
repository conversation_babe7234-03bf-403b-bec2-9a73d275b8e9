<script setup lang="ts">
import type { CreditPricingInfo, InitiationInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { addCreditPricingApi, editCreditPricingApi, getCompanyListApi, getCreditPricingInfoApi } from '#/api';
import RepaymentCalculation from '#/views/project/components/repayment-calculation.vue';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();
const RepaymentCalculationRef = ref();
const init = async (data: CreditPricingInfo) => {
  creditPricingForm.value = data.id
    ? await getCreditPricingInfoApi(data.id as number)
    : {
        ...data,
      };
  // 确保所有字段都正确初始化
  if (creditPricingForm.value.penaltyType === 'ladder') {
    creditPricingForm.value.penaltySteps = creditPricingForm.value.penaltySteps ?? [];
    await LadderGridApi.grid.reloadData(creditPricingForm.value.penaltySteps ?? []);
  }
  RepaymentCalculationRef.value.init();
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const FormRef = ref();
const creditPricingForm = ref<CreditPricingInfo>({});
const save = async () => {
  await RepaymentCalculationRef.value.save();
  await FormRef.value.validate();
  changeOkLoading(true);
  let api = addCreditPricingApi;
  if (creditPricingForm.value.id) {
    api = editCreditPricingApi;
  }
  const formData = cloneDeep(creditPricingForm.value);
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const selectInitiation = (_value: number, data: InitiationInfo) => {
  creditPricingForm.value.pricingName = `${data.projectName}-定价`;
};
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  pricingName: [{ required: true, message: '请输入定价名称' }],
  expectedUseAmount: [{ required: true, message: '请输入拟用信金额' }],
  creditCompanyCode: [{ required: true, message: '请选择用信主体', trigger: 'change' }],
  creditRate: [{ required: true, message: '请输入用信费率' }],
  compositeYieldRate: [{ required: true, message: '请输入综合收益率' }],
  serviceFeeAmount: [{ required: true, message: '请输入服务费' }],
  planningMethod: [{ required: true, message: '请选择还本付息计划规划方式', trigger: 'change' }],
  expectedLaunchDate: [{ required: true, message: '请选择预计业务投放日', trigger: 'change' }],
  expectedDueDate: [{ required: true, message: '请选择预估最后还款日', trigger: 'change' }],
  principalRepaymentMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  interestRepaymentMethod: [{ required: true, message: '请选择还息方式', trigger: 'change' }],
  principalPeriod: [{ required: true, message: '请选择分期还本频次', trigger: 'change' }],
  interestPeriod: [{ required: true, message: '请选择分期还息频次', trigger: 'change' }],
  repayPrincipalDay: [{ required: true, message: '请选择默认当期还本日', trigger: 'change' }],
  repayInterestDay: [{ required: true, message: '请选择默认当期还息日', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="用信定价" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="creditPricingForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联综合授信项目" name="projectId">
              <ApiComponent
                v-model="creditPricingForm.projectId as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                @change="selectInitiation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目类型" name="projectType">
              <a-select
                v-model:value="creditPricingForm.projectType"
                :options="dictStore.getDictList('FCT_PROJECT_TYPE')"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目定价名称" name="pricingName">
              <a-input v-model:value="creditPricingForm.pricingName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="客户类别" name="customerCategory">
              <a-select
                v-model:value="creditPricingForm.customerCategory"
                :options="dictStore.getDictList('FCT_CUSTOMER_CATEGORY')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="客户分级" name="customerLevel">
              <a-select
                v-model:value="creditPricingForm.customerLevel"
                :options="dictStore.getDictList('FCT_CUSTOMER_LEVEL')"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="行业属性" name="industryAttributes">
              <a-input v-model:value="creditPricingForm.industryAttributes" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信额度测算结果" name="creditCalculateAmount">
              <a-input v-model:value="creditPricingForm.creditCalculateAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="区域特性" name="areaCharacteristics">
              <a-input v-model:value="creditPricingForm.areaCharacteristics" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="底层项目" name="bottomProject">
              <a-input v-model:value="creditPricingForm.bottomProject" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="担保方式" name="guaranteeMethodDesc">
              <a-input v-model:value="creditPricingForm.guaranteeMethodDesc" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="其他情况" name="pricingOtherDesc" v-bind="fullProp">
              <a-textarea v-model:value="creditPricingForm.pricingOtherDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 授信批复方案 -->
        <BasicCaption content="授信批复方案" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="授信金额（元）" name="pricingCreditAmount">
              <a-input v-model:value="creditPricingForm.pricingCreditAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信期限（个月）" name="pricingCreditTerm">
              <a-input v-model:value="creditPricingForm.pricingCreditTerm" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信费率（%/年）" name="pricingCreditRate">
              <a-input v-model:value="creditPricingForm.pricingCreditRate" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信额度类型">
              {{ dictStore.formatter(creditPricingForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 用信定价方案 -->
        <BasicCaption content="用信定价方案" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="拟用信金额（元）" name="expectedUseAmount">
              <a-input v-model:value="creditPricingForm.expectedUseAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="用信费率（%/年）" name="creditRate">
              <a-input v-model:value="creditPricingForm.creditRate" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="服务费（元）" name="serviceFeeAmount">
              <a-input v-model:value="creditPricingForm.serviceFeeAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="用信主体" name="creditCompanyCode">
              <ApiComponent
                v-model="creditPricingForm.creditCompanyCode as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="综合收益率（%/年）" name="compositeYieldRate">
              <a-input v-model:value="creditPricingForm.compositeYieldRate" />
            </a-form-item>
          </a-col>
        </a-row>

        <RepaymentCalculation ref="RepaymentCalculationRef" v-model="creditPricingForm" />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
