<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delCreditPricingApi, getCreditPricingPageListApi } from '#/api';

import CreditPricingDetail from './credit-pricing-detail.vue';
import CreditPricingEdit from './credit-pricing-edit.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'pricingName',
      label: '用信定价名称',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'projectType',
      label: '项目类型',
      componentProps: {
        options: dictStore.getDictList('FCT_PROJECT_TYPE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'pricingName', title: '用信定价名称', minWidth: 200 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    { field: 'targetCompanyName', title: '合作企业', minWidth: 200 },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: '',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getCreditPricingPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: CreditPricingInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: CreditPricingInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: AccessInfo) => {
  openDetailPopup(true, row);
};
const del = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该用信定价，是否继续？',
    async onOk() {
      await delCreditPricingApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
          <a-button type="primary" danger @click="del">
            {{ $t('base.del') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <CreditPricingEdit @register="registerForm" @ok="editSuccess" />
    <CreditPricingDetail @register="registerDetail" />
  </Page>
</template>

<style></style>
