<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { watch } from 'vue';

import { DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';

const props = defineProps({ calculationForm: { type: Object, default: () => ({}) } });
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const dictStore = useDictStore();

const calculationGridOptions = {
  columns: [
    {
      field: 'repayPeriods',
      title: '还款期数',
      minWidth: '100px',
    },
    {
      field: 'repaymentItem',
      title: '还款项',
      minWidth: '120px',
      componentProps: {
        options: dictStore.getDictList('FCT_REPAY_REPAYMENT_ITEM'),
      },
    },
    {
      field: 'currentDate',
      title: '当期还本/付息日',
      minWidth: '180px',
    },
    {
      field: 'totalAmount',
      title: '当期净现金流(元)',
      minWidth: '180px',
    },
    {
      field: 'principalAmount',
      title: '应还本金(元)',
      minWidth: '180px',
    },
    {
      field: 'interestAmount',
      title: '应还利息(元)',
      minWidth: '180px',
    },
    {
      field: 'serviceAmount',
      title: '应收服务费(元)',
      minWidth: '180px',
    },
    {
      field: 'graceInterestAmount',
      title: '应还宽限期利息(元)',
      minWidth: '180px',
    },
    {
      field: 'overdueInterestAmount',
      title: '应还逾期罚息(元)',
      minWidth: '180px',
    },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [CalculationGrid, CalculationGridApi] = useVbenVxeGrid({
  gridOptions: calculationGridOptions,
});
watch(
  () => props.calculationForm,
  (val = {}) => {
    CalculationGridApi.grid.reloadData(val.detailList ?? []);
  },
  { deep: true },
);
</script>

<template>
  <div>
    <BasicCaption content="还本付息试算" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="还本付息计划规划方式">
        {{ dictStore.formatter(calculationForm.planningMethod, 'FCT_PLANNING_METHOD') }}
      </a-descriptions-item>
      <a-descriptions-item label="测算综合收益率（%/年）">
        {{ calculationForm.compositeYieldRate }}
      </a-descriptions-item>
      <a-descriptions-item label="预计业务投放日">
        {{ calculationForm.expectedLaunchDate }}
      </a-descriptions-item>
      <a-descriptions-item label="预估最后还款日">
        {{ calculationForm.expectedDueDate }}
      </a-descriptions-item>

      <template v-if="calculationForm.planningMethod === 'automatic'">
        <a-descriptions-item label="还本方式">
          {{ dictStore.formatter(calculationForm.principalRepaymentMethod, 'FCT_REPAY_PRINCIPAL_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="还息方式">
          {{ dictStore.formatter(calculationForm.interestRepaymentMethod, 'FCT_REPAY_INTEREST_METHOD') }}
        </a-descriptions-item>

        <a-descriptions-item label="分期还本频次" v-if="calculationForm.principalRepaymentMethod === 'regular'">
          {{ dictStore.formatter(calculationForm.principalPeriod, 'FCT_FREQUENCY') }}
        </a-descriptions-item>
        <a-descriptions-item label="分期还息频次" v-if="calculationForm.interestRepaymentMethod === 'regular'">
          {{ dictStore.formatter(calculationForm.interestPeriod, 'FCT_FREQUENCY') }}
        </a-descriptions-item>

        <a-descriptions-item label="默认当期还本日" v-if="calculationForm.principalRepaymentMethod === 'regular'">
          {{ dictStore.formatter(calculationForm.repayPrincipalDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-descriptions-item>
        <a-descriptions-item label="默认当期还息日" v-if="calculationForm.interestRepaymentMethod === 'regular'">
          {{ dictStore.formatter(calculationForm.repayInterestDay, 'FCT_CURRENT_REPAY_DATE') }}
        </a-descriptions-item>
      </template>

      <template v-if="calculationForm.planningMethod === 'manual'">
        <a-descriptions-item label="还款期数">
          {{ calculationForm.expectedRepayPeriods }}
        </a-descriptions-item>
      </template>

      <a-descriptions-item label="预估计息天数（天）">
        {{ calculationForm.expectedInterestDays }}
      </a-descriptions-item>

      <a-descriptions-item label="预估还款期数" v-if="calculationForm.planningMethod === 'automatic'">
        {{ calculationForm.expectedRepayPeriods }}
      </a-descriptions-item>
    </a-descriptions>

    <CalculationGrid />
  </div>
</template>

<style></style>
