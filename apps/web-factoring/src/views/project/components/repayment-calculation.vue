<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ProjectUseCalculationBO } from '#/api';

import { COL_SPAN_PROP } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { cloneDeep, getCombinedErrorMessagesString, isEmpty } from '@vben/utils';

import { SyncOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { calculationProject, calculationRateProject } from '#/api';

const calculationForm = defineModel<ProjectUseCalculationBO>({ type: Object, required: true });
const dictStore = useDictStore();
const colSpan = COL_SPAN_PROP;
const init = async () => {
  calculationForm.value.detailList = calculationForm.value.detailList ?? [];
  await CalculationGridApi.grid.reloadData(calculationForm.value.detailList ?? []);
};
const baseGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const expectedDateChange = () => {
  const { expectedLaunchDate, expectedDueDate } = calculationForm.value;

  if (expectedLaunchDate && expectedDueDate) {
    // 关键修复：确保转换为数字类型
    const startTimestamp = Number(expectedLaunchDate);
    const endTimestamp = Number(expectedDueDate);

    // 解析为日期对象
    const start = dayjs(startTimestamp);
    const end = dayjs(endTimestamp);

    // 计算天数差
    const daysDiff = end.diff(start, 'day');

    calculationForm.value.expectedInterestDays = Math.max(0, daysDiff);
  }
};
const calculationGridOptions = {
  columns: [
    {
      field: 'repayPeriods',
      title: '还款期数',
      minWidth: '100px',
    },
    {
      field: 'repaymentItem',
      title: '还款项',
      slots: { default: 'edit_repayment_item' },
      minWidth: '160px',
    },
    {
      field: 'currentDate',
      title: '当期还本/付息日',
      slots: { default: 'edit_current_date' },
      minWidth: '180px',
    },
    {
      field: 'totalAmount',
      title: '当期净现金流(元)',
      minWidth: '180px',
    },
    {
      field: 'principalAmount',
      title: '应还本金(元)',
      slots: { default: 'edit_principal_amount' },
      minWidth: '180px',
    },
    {
      field: 'interestAmount',
      title: '应还利息(元)',
      slots: { default: 'edit_interest_amount' },
      minWidth: '180px',
    },
    {
      field: 'serviceAmount',
      title: '应收服务费(元)',
      minWidth: '180px',
    },
    {
      field: 'graceInterestAmount',
      title: '应还宽限期利息(元)',
      minWidth: '180px',
    },
    {
      field: 'overdueInterestAmount',
      title: '应还逾期罚息(元)',
      minWidth: '180px',
    },
  ],
  editRules: {
    currentDate: [{ required: true, content: '请选择当期还本/付息日', trigger: 'change' }],
    totalAmount: [{ required: true, content: '请输入当期净现金流' }],
    principalAmount: [{ required: true, content: '请输入应还本金' }],
    interestAmount: [{ required: true, content: '请输入应还利息' }],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [CalculationGrid, CalculationGridApi] = useVbenVxeGrid({
  gridOptions: calculationGridOptions,
});
const exportData = () => {
  // 导出逻辑实现
};
const calculation = async () => {
  const formData = cloneDeep(calculationForm.value);
  formData.expectedLaunchDate = Number(formData.expectedLaunchDate);
  formData.expectedDueDate = Number(formData.expectedDueDate);
  const res = await calculationProject(formData);
  if (!isEmpty(res)) {
    res.forEach((item, index) => {
      item.currentDate = dayjs(item.currentDate).valueOf().toString();
      if (index === 0) {
        item.repaymentItemDisabled = true;
        item.currentDateDisabled = true;
        item.principalAmountDisabled = true;
        item.interestAmountDisabled = true;
      }
      if (index === res.length - 1) {
        item.repaymentItemDisabled = true;
        item.currentDateDisabled = true;
      }
    });
  }
  await CalculationGridApi.grid.reloadData(res);
  message.success('试算成功');
};
const calculationRate = async () => {
  const formData = {};
  const { visibleData } = CalculationGridApi.grid.getTableData();
  formData.detailList = visibleData || [];
  if (!isEmpty(formData.detailList)) {
    formData.detailList.forEach((item) => {
      item.currentDate = Number(item.currentDate);
    });
  }
  const res = await calculationRateProject(formData.detailList);
  calculationForm.value.xirrRate = res || '';
  message.success('试算成功');
};
const save = async () => {
  const errMap = await CalculationGridApi.grid.validate(true);
  if (errMap) {
    const errMessage = getCombinedErrorMessagesString(errMap);
    if (errMessage) {
      message.error(errMessage);
    }
  }
  const { visibleData } = CalculationGridApi.grid.getTableData();
  calculationForm.value.detailList = visibleData || [];
  return calculationForm.value;
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <BasicCaption content="还本付息试算" />

    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="还本付息计划规划方式" name="planningMethod">
          <a-radio-group
            v-model:value="calculationForm.planningMethod"
            :options="dictStore.getDictList('FCT_PLANNING_METHOD')"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="测算综合收益率（%/年）" name="xirrRate">
          <a-input v-model:value="calculationForm.xirrRate" disabled>
            <template #addonAfter>
              <SyncOutlined @click="calculationRate" />
            </template>
          </a-input>
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="预计业务投放日" name="expectedLaunchDate">
          <a-date-picker
            v-model:value="calculationForm.expectedLaunchDate"
            value-format="x"
            class="w-full"
            @change="expectedDateChange"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="预估最后还款日" name="expectedDueDate">
          <a-date-picker
            v-model:value="calculationForm.expectedDueDate"
            value-format="x"
            class="w-full"
            @change="expectedDateChange"
          />
        </a-form-item>
      </a-col>
      <template v-if="calculationForm.planningMethod === 'automatic'">
        <a-col v-bind="colSpan">
          <a-form-item label="还本方式" name="principalRepaymentMethod">
            <a-select
              v-model:value="calculationForm.principalRepaymentMethod"
              :options="dictStore.getDictList('FCT_REPAY_PRINCIPAL_METHOD')"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="还息方式" name="interestRepaymentMethod">
            <a-select
              v-model:value="calculationForm.interestRepaymentMethod"
              :options="dictStore.getDictList('FCT_REPAY_INTEREST_METHOD')"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="calculationForm.principalRepaymentMethod === 'regular'">
          <a-form-item label="分期还本频次" name="principalPeriod">
            <a-select
              v-model:value="calculationForm.principalPeriod"
              :options="dictStore.getDictList('FCT_FREQUENCY')"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="calculationForm.interestRepaymentMethod === 'regular'">
          <a-form-item label="分期还息频次" name="interestPeriod">
            <a-select
              v-model:value="calculationForm.interestPeriod"
              :options="dictStore.getDictList('FCT_FREQUENCY')"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="calculationForm.principalRepaymentMethod === 'regular'">
          <a-form-item label="默认当期还本日" name="repayPrincipalDay">
            <a-select
              v-model:value="calculationForm.repayPrincipalDay"
              :options="dictStore.getDictList('FCT_CURRENT_REPAY_DATE')"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="calculationForm.interestRepaymentMethod === 'regular'">
          <a-form-item label="默认当期还息日" name="repayInterestDay">
            <a-select
              v-model:value="calculationForm.repayInterestDay"
              :options="dictStore.getDictList('FCT_CURRENT_REPAY_DATE')"
            />
          </a-form-item>
        </a-col>
      </template>
      <template v-if="calculationForm.planningMethod === 'manual'">
        <a-col v-bind="colSpan">
          <a-form-item label="还款期数" name="expectedRepayPeriods">
            <a-input v-model:value="calculationForm.expectedRepayPeriods" />
          </a-form-item>
        </a-col>
      </template>
      <a-col v-bind="colSpan">
        <a-form-item label="预估计息天数（天）" name="expectedInterestDays">
          <a-input v-model:value="calculationForm.expectedInterestDays" disabled />
        </a-form-item>
      </a-col>
      <template v-if="calculationForm.planningMethod === 'automatic'">
        <a-col v-bind="colSpan">
          <a-form-item label="预估还款期数">
            <a-input v-model:value="calculationForm.expectedRepayPeriods" disabled />
          </a-form-item>
        </a-col>
      </template>
    </a-row>
    <CalculationGrid>
      <template #toolbarTools>
        <a-space>
          <a-button type="primary" @click="calculation">开始试算</a-button>
          <a-button type="primary" @click="exportData">导出</a-button>
        </a-space>
      </template>
      <template #edit_repayment_item="{ row }">
        <a-select
          v-model:value="row.repaymentItem"
          :options="dictStore.getDictList('FCT_REPAY_REPAYMENT_ITEM')"
          :disabled="row.repaymentItemDisabled"
          class="w-full"
        />
      </template>
      <template #edit_current_date="{ row }">
        <a-date-picker
          v-model:value="row.currentDate"
          value-format="x"
          class="w-full"
          :allow-clear="false"
          :disabled="row.currentDateDisabled"
        />
      </template>
      <template #edit_principal_amount="{ row }">
        <a-input-number
          :controls="false"
          v-model:value="row.principalAmount"
          :disabled="row.principalAmountDisabled"
          class="w-full"
          :default-value="0"
          :min="0"
          :precision="2"
        />
      </template>
      <template #edit_interest_amount="{ row }">
        <a-input-number
          :controls="false"
          v-model:value="row.interestAmount"
          :disabled="row.interestAmountDisabled"
          class="w-full"
          :default-value="0"
          :min="0"
          :precision="2"
        />
      </template>
    </CalculationGrid>
  </div>
</template>
