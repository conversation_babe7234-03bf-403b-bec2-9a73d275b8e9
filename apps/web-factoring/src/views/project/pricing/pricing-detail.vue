<script setup lang="ts">
import type { PricingInfo } from '#/api';

import { ref } from 'vue';

import {
  BASE_PAGE_CLASS_NAME,
  COL_SPAN_PROP,
  DESCRIPTIONS_PROP,
  FORM_PROP,
  FULL_FORM_ITEM_PROP,
} from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { getPricingInfoApi, recheckComprehensivePricingApi, recheckSinglePricingApi } from '#/api';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();
const pageType = ref('detail');
const init = async (data: PricingInfo) => {
  pageType.value = data.pageType ?? 'detail';
  pricingForm.value = data.id
    ? await getPricingInfoApi(data.id as number)
    : {
        ...data,
      };
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const FormRef = ref();
const pricingForm = ref<PricingInfo>({});
const save = async (type) => {
  await FormRef.value.validate();
  changeOkLoading(true);
  let api;
  if (pricingForm.value.id) {
    api = pricingForm.value.projectType === 'comprehensive' ? recheckComprehensivePricingApi : recheckSinglePricingApi;
  }
  const formData = cloneDeep(pricingForm.value);
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const reject = async () => {
  changeOkLoading(true);
  try {
    const res = await recheckSinglePricingApi(pricingForm.value as PricingInfo);
    message.success('驳回成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const rules = {
  pricingFloatingRatio: [{ required: true, message: '请输入浮动定价', trigger: 'blur' }],
  comprehensiveRate: [{ required: true, message: '请输入项目定价综合收益率', trigger: 'blur' }],
  pricingDesc: [{ required: true, message: '请输入定价方案说明', trigger: 'blur' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" title="项目定价信息" @register="registerPopup">
    <template v-if="pageType === 'recheck'" #insertToolbar>
      <a-space>
        <a-button type="primary" danger @click="reject">驳回</a-button>
        <a-button type="primary" danger @click="save()">保存</a-button>
        <a-button type="primary" danger @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="DESCRIPTIONS_PROP" class="mt-4">
        <a-descriptions-item label="关联项目">
          {{ pricingForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目类型">
          {{ dictStore.formatter(pricingForm.projectType, 'FCT_PROJECT_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="项目定价名称">
          {{ pricingForm.pricingName }}
        </a-descriptions-item>
        <a-descriptions-item label="客户类别">
          {{ dictStore.formatter(pricingForm.customerCategory, 'FCT_CUSTOMER_CATEGORY') }}
        </a-descriptions-item>
        <a-descriptions-item label="客户分级">
          {{ dictStore.formatter(pricingForm.customerLevel, 'FCT_CUSTOMER_LEVEL') }}
        </a-descriptions-item>
        <a-descriptions-item label="行业属性">
          {{ pricingForm.industryAttributes }}
        </a-descriptions-item>
        <a-descriptions-item label="授信额度测算结果">
          {{ pricingForm.creditCalculateAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="区域特性">
          {{ pricingForm.areaCharacteristics }}
        </a-descriptions-item>
        <a-descriptions-item label="底层项目">
          {{ pricingForm.bottomProject }}
        </a-descriptions-item>
        <a-descriptions-item label="担保方式">
          {{ pricingForm.guaranteeMethodDesc }}
        </a-descriptions-item>
        <a-descriptions-item label="其他情况" :span="2">
          {{ pricingForm.pricingOtherDesc }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption :content="`一般定价方案（${dictStore.formatter(pricingForm.projectType, 'FCT_PROJECT_TYPE')}）`" />
      <a-descriptions v-bind="DESCRIPTIONS_PROP" class="mt-4">
        <template v-if="pricingForm.projectType === 'comprehensive'">
          <a-descriptions-item label="授信额度（元）">
            {{ pricingForm.pricingCreditAmount }}
          </a-descriptions-item>
          <a-descriptions-item label="授信期限（个月）">
            {{ pricingForm.pricingCreditTerm }}
          </a-descriptions-item>
          <a-descriptions-item label="授信费率（%）">
            {{ pricingForm.pricingCreditRate }}
          </a-descriptions-item>
          <a-descriptions-item label="授信额度类型">
            {{ dictStore.formatter(pricingForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
          </a-descriptions-item>
        </template>
        <a-descriptions-item label="基准定价（%/年）">
          {{ pricingForm.pricingBasicRatio }}
        </a-descriptions-item>
        <a-descriptions-item label="回款方式">
          {{ dictStore.formatter(pricingForm.paymentCollectionMethod, 'FCT_PAYMENT_COLLECTION_METHOD') }}
        </a-descriptions-item>
      </a-descriptions>
      <a-form
        ref="FormRef"
        class="mt-5"
        :model="pricingForm"
        :rules="rules"
        v-bind="formProp"
        v-if="pageType === 'recheck'"
      >
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="浮动定价（%/年）" name="pricingFloatingRatio">
              <a-input v-model:value="pricingForm.pricingFloatingRatio" />
            </a-form-item>
          </a-col>
          <template v-if="pricingForm.projectType === 'single'">
            <a-col v-bind="colSpan">
              <a-form-item label="项目定价综合收益率（%/年）" name="comprehensiveRate">
                <a-input v-model:value="pricingForm.comprehensiveRate" />
              </a-form-item>
            </a-col>
          </template>
          <a-col :span="24">
            <a-form-item label="定价方案说明" name="pricingDesc" v-bind="fullProp">
              <a-textarea v-model:value="pricingForm.pricingDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
