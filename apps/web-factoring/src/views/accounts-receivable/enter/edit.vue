<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { defaultsDeep } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {} from '#/api';

const emit = defineEmits(['register', 'ok']);
const creditorTableData = ref([
  {
    projectCode: '001',
    projectName: '项目检查1',
    businessStructure: '已完成',
    projectModel: '一切正常，无异常情况',
  },
  {
    projectCode: '002',
    projectName: '项目检查2',
    businessStructure: '进行中',
    projectModel: '部分环节未完成，需跟进',
  },
  {
    projectCode: '003',
    projectName: '项目检查3',
    businessStructure: '未开始',
    projectModel: '准备工作尚未完成',
  },
]);

// 根据接口定义初始化产品信息
const defaultForm: Partial<InitiationBaseInfo> = {
  project: {
    projectName: '',
    businessStructure: undefined,
    projectModel: undefined,
    executorCompanyId: undefined,
    executorCompanyName: '',
    businessManagerId: undefined,
    businessManagerName: undefined,
    supplierCompanyName: undefined,
    purchaserCompanyName: undefined,
    creditCompanyName: undefined,
    isDeposit: undefined,
    isGoodsControlMode: undefined,
    serviceFeeRate: '',
    paymentTermDays: '',
    expectedProjectScale: '',
    businessDate: undefined,
    estimatedEndDate: undefined,
    purchaseMode: undefined,
    projectCode: '',
    remarks: '',
  },
  attachmentFiles: [],
};

const detailForm = reactive<Partial<InitiationBaseInfo>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  businessStructure: [{ required: true, message: '请选择业务结构', trigger: 'change' }],
  projectModel: [{ required: true, message: '请选择项目模式', trigger: 'change' }],
  executorCompanyName: [{ required: true, message: '请输入贸易执行企业', trigger: 'change' }],
  businessManagerName: [{ required: true, message: '请选择业务负责人', trigger: 'change' }],
  supplierCompanyName: [{ required: true, message: '请选择上游企业', trigger: 'change' }],
  purchaserCompanyName: [{ required: true, message: '请选择下游企业', trigger: 'change' }],
  creditCompanyName: [{ required: true, message: '请选择终端企业', trigger: 'change' }],
  projectAddress: [{ required: true, message: '请输入项目地点', trigger: 'change' }],
  isDeposit: [{ required: true, message: '请选择是否有保证金', trigger: 'change' }],
  purchaseMode: [{ required: true, message: '请选择采购模式', trigger: 'change' }],
  isGoodsControlMode: [{ required: true, message: '请选择是否是控货模式', trigger: 'change' }],
  serviceFeeRate: [{ required: true, message: '请输入服务费率', trigger: 'change' }],
  paymentTermDays: [{ required: true, message: '请输入账期', trigger: 'change' }],
};
const title = computed(() => {
  return detailForm.project.id ? '编辑项目' : '新增项目';
});

const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  try {
    message.success($t('base.resSuccess'));
    emit('ok', '');
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner();
const labelCol = { style: { width: '150px' } };

const gridContractTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  columns: [
    { field: 'projectCode', title: '基础合同名称' },
    { field: 'projectName', title: '基础合同编号' },
    { field: 'businessStructure', title: '基础合同类型' },
    { field: 'projectModel', title: '基础合同金额（元）' },
    { field: 'supplierCompanyName', title: '未付款金额（元）' },
    { field: 'projectModel', title: '拟转让应收账款金额（元）' },
    { field: 'supplierCompanyName', title: '凭证名称' },
    { field: 'projectModel', title: '凭证号码' },
    { field: 'supplierCompanyName', title: '签署日期' },
    {
      field: 'action',
      title: '上传附件',
      fixed: 'right',
      width: 160,
      slots: { default: 'upLoadFile' },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  proxyConfig: {
    // ajax: {
    //   query: async ({ page }, formValues) => {
    //     return await getProjectPageApi({
    //       current: page.currentPage,
    //       size: page.pageSize,
    //       ...formValues,
    //     });
    //   },
    // },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const gridInvoiceTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  data: [
    {
      projectCode: '001',
      projectName: '发票类型',
      businessStructure: '已完成',
      projectModel: '一切正常，无异常情况',
    },
    {
      projectCode: '002',
      projectName: '项目检查2',
      businessStructure: '进行中',
      projectModel: '部分环节未完成，需跟进',
    },
    {
      projectCode: '003',
      projectName: '项目检查3',
      businessStructure: '未开始',
      projectModel: '准备工作尚未完成',
    },
  ],
  columns: [
    { field: 'projectCode', title: '发票类型' },
    { field: 'projectName', title: '发票号码' },
    { field: 'projectCode', title: '发票代码' },
    { field: 'projectName', title: '不含税金额（元）' },
    { field: 'projectCode', title: '含税金额（元）' },
    { field: 'projectName', title: '开票日期' },
    { field: 'projectCode', title: '购买方' },
    { field: 'projectName', title: '销售方' },
    { field: 'projectCode', title: '校验码后6位' },
    { field: 'projectName', title: '验真结果' },
    {
      field: 'action',
      title: '发票附件',
      fixed: 'right',
      width: 160,
      slots: { default: 'upLoadFile' },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const [ContractTable, gridApiSupplier] = useVbenVxeGrid({ gridOptions: gridContractTable });
const [InvoiceTable, gridApiPurchaser] = useVbenVxeGrid({ gridOptions: gridInvoiceTable });
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm.project"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="关联项目" name="businessStructure">
            <Select v-model:value="detailForm.project.businessStructure" :options="[]" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收款账编号" name="projectName">
            <Input v-model:value="detailForm.project.projectName" placeholder="请输入应收款账编号" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务类型" name="projectName">
            <Select v-model:value="detailForm.project.businessStructure" :options="[]" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="债权人" name="projectName">
            <Input v-model:value="detailForm.project.projectName" placeholder="请输入债权人" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="债务人" name="projectName">
            <Input v-model:value="detailForm.project.projectName" placeholder="请输入债务人" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收款账名称" name="isGoodsControlMode">
            <Input v-model:value="detailForm.project.projectName" placeholder="请输入应收款账名称" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="债权人、债务人关系" name="isGoodsControlMode">
            <Input v-model:value="detailForm.project.projectName" placeholder="请输入债权人、债务人关系" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款金额（元）" name="receiptDate">
            <Input v-model:value="detailForm.project.projectName" placeholder="请输入应收账款金额" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款到期日" name="isGoodsControlMode">
            <DatePicker v-model:value="detailForm.receiptDate" value-format="YYYY-MM-DD" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款期限（个月）" name="isGoodsControlMode">
            <Input v-model:value="detailForm.project.projectName" placeholder="请输入应收账款期限" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款描述" name="isGoodsControlMode">
            <Textarea v-model:value="detailForm.operationalRiskSummary" :rows="4" placeholder="请输入应收账款描述" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="基础合同" />
      <ContractTable>
        <template #toolbarTools>
          <Button class="mr-2" type="primary" @click="() => addAccount(gridApiSupplier)">增行</Button>
          <Button class="mr-2" danger @click="() => removeAccount(gridApiSupplier)">删行</Button>
        </template>
      </ContractTable>
      <BasicCaption content="发票" />
      <InvoiceTable v-model="creditorTableData">
        <template #toolbarTools>
          <Button class="mr-2" type="primary" @click="() => addAccount(gridApiPurchaser)">增行</Button>
          <Button class="mr-2" danger @click="() => removeAccount(gridApiPurchaser)">删行</Button>
        </template>
      </InvoiceTable>
      <BasicCaption content="应收账款佐证材料" />
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
