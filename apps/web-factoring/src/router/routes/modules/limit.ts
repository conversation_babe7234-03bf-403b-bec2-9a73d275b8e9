import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '额度管理',
    },
    name: 'limitAdmin',
    path: '/limitAdmin',
    children: [
      {
        name: 'regionalCalculation',
        path: '/limitAdmin/regionalCalculation',
        component: () => import('#/views/limit-admin/regionalCalculation.vue'),
        meta: {
          title: '地区额度测算',
        },
      },
      {
        name: 'enterpriseCalculation',
        path: '/limitAdmin/enterpriseCalculation',
        component: () => import('#/views/limit-admin/enterpriseCalculation.vue'),
        meta: {
          title: '企业额度测算',
        },
      },
    ],
  },
];

export default routes;
