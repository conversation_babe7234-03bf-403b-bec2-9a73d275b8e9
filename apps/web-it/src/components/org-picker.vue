<script setup lang="ts">
import type { TreeProps } from 'ant-design-vue';

import type { OrgInfo } from '#/api';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Col, InputSearch, Row, Select, Tree } from 'ant-design-vue';

import { getOrgListApi } from '#/api';

const modal = defineModel({ type: String });
const [Modal, modalApi] = useVbenModal({ isOpen: true });
const pickOrg = () => {
  modalApi.open();
};
const pickSearchValue = ref('');
const loading = ref({
  search: false,
});
const treeData = ref([]);
const expandedKeys = ref<string[]>([]);
const getOrgList = async () => {
  loading.value.search = true;
  try {
    treeData.value = await getOrgListApi({ parentId: '-1' });
    expandedKeys.value = treeData.value.map((item: OrgInfo) => item.id);
  } finally {
    loading.value.search = false;
  }
};
getOrgList();
const onLoadData: TreeProps['loadData'] = async (treeNode) => {
  if (!treeNode.dataRef) {
    return;
  }
  if (treeNode.dataRef?.children) {
    return;
  }
  treeNode.dataRef.children = await getOrgListApi({
    parentId: treeNode.dataRef.id,
  });
  treeData.value = [...treeData.value];
};
const fieldNames = {
  key: 'id',
  title: 'name',
  children: 'children',
};
</script>

<template>
  <div>
    <Select
      v-model:value="modal"
      :open="true"
      popup-class-name="hidden"
      @click="pickOrg"
    />
    <Modal title="选择组织" class="w-[600px]">
      <Row>
        <Col :span="12">
          <InputSearch
            v-model:value="pickSearchValue"
            :loading="loading.search"
          />
          <Tree
            v-model:expanded-keys="expandedKeys"
            :tree-data="treeData"
            :load-data="onLoadData"
            :field-names="fieldNames"
            auto-expand-parent
          />
        </Col>
        <Col :span="12" />
      </Row>
    </Modal>
  </div>
</template>

<style scoped></style>
