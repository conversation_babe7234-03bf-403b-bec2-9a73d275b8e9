import { ref } from 'vue';

import { defineStore } from 'pinia';

import { getDictListApi } from '#/api';

export interface DictInfo {
  code: string;
  dictName: string;
  dictValue: number | string;
  dictColor?: string;
  isTag?: number;
}
export interface DictInfoExtended extends DictInfo {
  label?: string;
  value?: number | string;
}

const localDictList: DictInfoExtended[] = [
  { code: 'baseEnableType', dictValue: 1, dictName: '启用', isTag: 1, dictColor: 'success' },
  { code: 'baseEnableType', dictValue: 0, dictName: '禁用', isTag: 1, dictColor: 'error' },
  { code: 'dictType', dictValue: 'SYS', dictName: '系统字典' },
  { code: 'dictType', dictValue: 'BIZ', dictName: '业务字典' },
  { code: 'orgType', dictValue: 'COMPANY', dictName: '企业' },
  { code: 'orgType', dictValue: 'DEPARTMENT', dictName: '部门' },
  { code: 'userStatus', dictValue: 1, dictName: '启用', isTag: 1, dictColor: 'success' },
  { code: 'userStatus', dictValue: 0, dictName: '锁定', isTag: 1, dictColor: 'warning' },
  { code: 'userStatus', dictValue: -1, dictName: '禁用', isTag: 1, dictColor: 'error' },
  { code: 'genderType', dictValue: 'SECRET', dictName: '保密' },
  { code: 'genderType', dictValue: 'MALE', dictName: '男' },
  { code: 'genderType', dictValue: 'FEMALE', dictName: '女' },
  { code: 'dictEditType', dictValue: 'ALL', dictName: '完全允许', isTag: 1, dictColor: 'success' },
  { code: 'dictEditType', dictValue: 'STATUS', dictName: '仅修改状态', isTag: 1, dictColor: 'warning' },
  { code: 'dictEditType', dictValue: 'NAME', dictName: '仅修改名称', isTag: 1, dictColor: 'warning' },
  { code: 'dictEditType', dictValue: 'NONE', dictName: '不可修改', isTag: 1, dictColor: 'error' },
];

export const useDictStore = defineStore(
  'dict',
  () => {
    const dictList = ref<DictInfo[]>([]);
    async function fetchDict() {
      try {
        const dictListRes = await getDictListApi();
        dictList.value = [...dictListRes, ...localDictList];
      } catch (error) {
        console.error(error);
      }
    }
    function getDictList(code: string) {
      const list = dictList.value.filter((item) => item.code === code);
      list.forEach((item: DictInfoExtended) => {
        item.label = item.dictName;
        item.value = item.dictValue;
      });
      return list;
    }
    function formatter(dictValue: string, code: string) {
      const dictList = getDictList(code);
      return dictList.find((item) => item.dictValue === dictValue)?.dictName || dictValue;
    }
    function dictItemInfo(dictValue: string, code: string) {
      const dictList = getDictList(code);
      return dictList.find((item) => item.dictValue === dictValue);
    }
    function $reset() {}
    return { $reset, dictList, fetchDict, getDictList, formatter, dictItemInfo };
  },
  {
    persist: {
      // 持久化
      pick: ['dictList'],
    },
  },
);
