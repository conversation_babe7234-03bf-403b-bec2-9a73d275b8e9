import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'tdesign:system-code',
      title: $t('page.system.title'),
    },
    name: 'System',
    path: '/system',
    children: [
      {
        name: 'SystemConfig',
        path: '/system/config',
        component: () => import('#/views/system/config/index.vue'),
        meta: {
          icon: 'hugeicons:configuration-01',
          title: $t('page.system.config'),
        },
      },
      {
        name: 'SystemResource',
        path: '/system/resource',
        component: () => import('#/views/system/resource/index.vue'),
        meta: {
          icon: 'carbon:software-resource-cluster',
          title: $t('page.system.resource'),
        },
      },
    ],
  },
];

export default routes;
