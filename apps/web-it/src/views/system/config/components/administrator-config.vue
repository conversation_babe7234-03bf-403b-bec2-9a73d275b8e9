<script setup lang="ts">
import { ref, unref } from 'vue';

import FeUserSelect from '@vben/fe-ui/components/Fe/Organize/src/UserSelect.vue';
import { $t } from '@vben/locales';

import { Button, Col, Form, FormItem, message, Row } from 'ant-design-vue';

import { getAdminListApi, getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, saveAdminApi } from '#/api';

const adminList = ref<string[]>([]);
const getAdminList = async () => {
  const resList = await getAdminListApi();
  const adminIds: string[] = [];
  resList.forEach((item: { id: string }) => {
    adminIds.push(item.id);
  });
  adminList.value = adminIds;
};
getAdminList();
const loading = ref({
  save: false,
});
const save = async () => {
  try {
    loading.value.save = true;
    await saveAdminApi(unref(adminList));
    message.success($t('base.resSuccess'));
  } finally {
    loading.value.save = false;
  }
};
</script>

<template>
  <Row>
    <Col :span="12">
      <Form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <FormItem label="管理员">
          <FeUserSelect
            v-model:value="adminList"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi }"
          />
        </FormItem>
        <FormItem :wrapper-col="{ span: 24 }">
          <div class="text-right">
            <Button :loading="loading.save" type="primary" @click="save">保存</Button>
          </div>
        </FormItem>
      </Form>
    </Col>
  </Row>
</template>

<style></style>
