<script lang="ts" setup>
import type { TreeActionType } from '@vben/fe-ui';
import type { Nullable } from '@vben/types';

import { nextTick, reactive, ref, toRefs, unref } from 'vue';

import { BasicModal, BasicTree, useModalInner } from '@vben/fe-ui';
import { useMessage } from '@vben/fe-ui/hooks/web/useMessage';

import { getFolderTreeApi, moveToApi } from '#/api';
import folderImg from '#/assets/images/document/folder.png';

interface State {
  treeData: any[];
  loading: boolean;
  ids: string;
  toId: string;
}

const emit = defineEmits(['register', 'reload']);
const { createMessage } = useMessage();
const [registerModal, { changeOkLoading, closeModal }] = useModalInner(init);
const treeRef = ref<Nullable<TreeActionType>>(null);
const state = reactive<State>({
  treeData: [],
  loading: false,
  ids: '',
  toId: '',
});
const { treeData, loading } = toRefs(state);

function init(data) {
  state.ids = data.ids;
  state.toId = data.parentId === '0' ? '-1' : data.parentId;
  state.loading = true;
  getFolderTreeApi(state.ids).then((res) => {
    state.treeData = res.list;
    state.loading = false;
    nextTick(() => {
      getTree().setSelectedKeys([state.toId]);
    });
  });
}
function getTree() {
  const tree = unref(treeRef);
  if (!tree) throw new Error('tree is null!');
  return tree;
}
function handleSelect(keys) {
  if (keys.length === 0) return (state.toId = '');
  if (state.toId === keys[0]) return;
  state.toId = keys[0];
}
function handleSubmit() {
  const toId = state.toId === '-1' ? '0' : state.toId;
  if (!toId) return createMessage.warning('请选择移动目录');
  changeOkLoading(true);
  moveToApi({ toId, ids: state.ids })
    .then((res) => {
      createMessage.success('移动成功');
      changeOkLoading(false);
      closeModal();
      emit('reload');
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="移动"
    :width="450"
    @ok="handleSubmit"
    class="fe-tree-modal"
    ok-text="移动到此"
  >
    <div class="h-350px">
      <BasicTree
        class="tree-main"
        :tree-data="treeData"
        default-expand-all
        @select="handleSelect"
        ref="treeRef"
        :loading="loading"
      >
        <template #title="{ fullName }"> <img :src="folderImg" class="mr-1 h-4 w-4" />{{ fullName }} </template>
      </BasicTree>
    </div>
  </BasicModal>
</template>
<style lang="less">
.fe-tree-modal {
  .ant-modal-body {
    & > .scrollbar {
      padding: 20px !important;
    }
  }
}
</style>
