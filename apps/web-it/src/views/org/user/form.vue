<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { ConfigInfo } from '@vben/types';

import type { PositionInfo, RoleInfo, UserInfo } from '#/api';

import { computed, ref } from 'vue';

import { BasicHelp, BasicPopup, FeOrganizeSelect, FeUserSelect, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { Alert, Col, Form, FormItem, Input, InputNumber, message, Row, Select, Textarea } from 'ant-design-vue';

import {
  addUserApi,
  editUserApi,
  getConfigInfoApi,
  getOrgListApi,
  getOrgListPyIdsApi,
  getPositionListByOrgIdsApi,
  getRoleListByOrgIdsApi,
  getUserInfoByIdsApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
} from '#/api';

const emit = defineEmits(['register', 'ok']);
const { getDictList } = useDictStore();
const defaultForm = {
  userName: '',
  organIds: [],
  postIds: [],
  roleIds: [],
  sortCode: 0,
  status: 1,
};
const userInfo = ref<Partial<UserInfo>>(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
const rules: Record<string, Rule[]> = {
  userName: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  realName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  organIds: [{ required: true, message: '请选择组织', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
};
const title = computed(() => {
  return userInfo.value.id ? `编辑${userInfo.value.realName ?? ''}` : '新增人员';
});
const init = (data: UserInfo) => {
  userInfo.value = defaultsDeep(data, defaultForm);
  changeOrg(userInfo.value.organIds, undefined, true);
};
const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  changeOkLoading(true);
  let api = addUserApi;
  if (userInfo.value.id) {
    api = editUserApi;
  }
  try {
    const res = await api(userInfo.value as UserInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
const postList = ref([]);
const roleList = ref([]);
const changeOrg = async (orgIdList: string[] = [], _?: UserInfo[], init = false) => {
  if (!init) {
    postList.value = [];
    userInfo.value.postIds = [];
    roleList.value = [];
    userInfo.value.roleIds = [];
  }
  if (orgIdList.length > 0) {
    const postRes = await getPositionListByOrgIdsApi(orgIdList);
    postRes.forEach((item: { name: string; organName: string; postList: PositionInfo[] }) => {
      item.name = item.organName;
      if (!item.postList) {
        item.postList = [];
      }
    });
    postList.value = postRes;
    const roleRes = await getRoleListByOrgIdsApi(orgIdList);
    roleRes.forEach((item: { name: string; organName: string; roleList: RoleInfo[] }) => {
      item.name = item.organName;
      if (!item.roleList) {
        item.roleList = [];
      }
    });
    roleList.value = roleRes;
  }
};
const systemConfig = ref<Partial<ConfigInfo>>({});
const getConfigInfo = async () => {
  systemConfig.value = await getConfigInfoApi();
};
getConfigInfo();
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="userInfo"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <Alert :message="`新用户默认初始密码是${systemConfig.newUserDefaultPassword ?? ''}`" type="warning" show-icon />
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="账号" name="userName">
            <Input v-model:value="userInfo.userName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="姓名" name="realName">
            <Input v-model:value="userInfo.realName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="手机" name="mobile">
            <Input v-model:value="userInfo.mobile" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="邮箱" name="email">
            <Input v-model:value="userInfo.email" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="工作信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="组织" name="organIds">
            <FeOrganizeSelect
              v-model:value="userInfo.organIds"
              :api="{ getOrgListApi, getOrgListPyIdsApi }"
              multiple
              @change="changeOrg"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="直属上级" name="managerId">
            <FeUserSelect
              v-model:value="userInfo.managerId"
              :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi }"
            />
            <!--<Input v-model:value="userInfo.managerId" />-->
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="岗位" name="postIds">
            <Select
              v-model:value="userInfo.postIds"
              mode="tags"
              :options="postList"
              :field-names="{ label: 'name', value: 'id', options: 'postList' }"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="角色" name="roleIds">
            <Select
              v-model:value="userInfo.roleIds"
              mode="tags"
              :options="roleList"
              :field-names="{ label: 'name', value: 'id', options: 'roleList' }"
            />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="其他信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem name="code">
            <template #label>
              工号
              <BasicHelp text="成员的唯一标识，填写后可修改" />
            </template>
            <Input v-model:value="userInfo.code" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="性别" name="gender">
            <Select v-model:value="userInfo.gender" :options="getDictList('genderType')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="状态" name="status">
            <Select v-model:value="userInfo.status" :options="getDictList('userStatus')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="排序" name="sortCode">
            <InputNumber v-model:value="userInfo.sortCode" :min="0" :step="1" :precision="0" class="w-full" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="说明" name="description" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
            <Textarea v-model:value="userInfo.description" />
          </FormItem>
        </Col>
      </Row>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
