<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { RoleInfo } from '#/api';

import { ref } from 'vue';

import FeOrganizeSelect from '@vben/fe-ui/components/Fe/Organize/src/OrganizeSelectAsync.vue';
import { defaultsDeep } from '@vben/utils';

import { Form, FormItem, Input, InputNumber, Select, SelectOption, Switch, Textarea } from 'ant-design-vue';

const roleForm = ref<Partial<RoleInfo>>({
  sortCode: 0,
  enabled: 1,
  organIdList: [],
  description: '',
});
const roleFormRef = ref();
const onSubmit = async () => {
  await roleFormRef.value.validate();
  return roleForm.value;
};
const rules: Record<string, Rule[]> = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入角色编码', trigger: 'blur' }],
  globalMark: [{ required: true, message: '请选择角色类型', trigger: 'change' }],
  organIdList: [{ required: true, message: '请选择所属组织', trigger: 'change' }],
};
const init = (data: Partial<RoleInfo>) => {
  roleForm.value = defaultsDeep(data, { sortCode: 0, enabled: 1, organIdList: [] });
};
defineExpose({
  onSubmit,
  init,
});
</script>

<template>
  <Form
    ref="roleFormRef"
    :model="roleForm"
    :label-col="{ span: 6 }"
    :wrapper-col="{ span: 18 }"
    :colon="false"
    :rules="rules"
    autocomplete="off"
  >
    <FormItem label="角色名称" name="name">
      <Input v-model:value="roleForm.name" />
    </FormItem>
    <FormItem label="角色编码" name="code">
      <Input v-model:value="roleForm.code" />
    </FormItem>
    <FormItem label="角色类型" name="globalMark">
      <Select v-model:value="roleForm.globalMark">
        <SelectOption :value="1">全局</SelectOption>
        <SelectOption :value="0">组织</SelectOption>
      </Select>
    </FormItem>
    <FormItem v-if="roleForm.globalMark === 0" label="所属组织" name="organIdList">
      <FeOrganizeSelect v-model:value="roleForm.organIdList" multiple />
    </FormItem>
    <FormItem label="排序">
      <InputNumber
        v-model:value="roleForm.sortCode"
        :controls="false"
        :min="0"
        :precision="0"
        :step="1"
        class="w-full"
      />
    </FormItem>
    <FormItem label="状态">
      <Switch v-model:checked="roleForm.enabled" :checked-value="1" :un-checked-value="0" />
    </FormItem>
    <FormItem label="说明">
      <Textarea v-model:value="roleForm.description" :auto-size="{ minRows: 3, maxRows: 5 }" />
    </FormItem>
  </Form>
</template>

<style></style>
