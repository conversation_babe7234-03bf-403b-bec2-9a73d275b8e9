<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { AppInfo } from '@vben/types';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { RoleInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useModal } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addRoleApi, deleteRoleApi, editRoleApi, getRolePageListApi } from '#/api';
import Member from '#/components/Member.vue';
import RoleForm from '#/views/org/role/components/role-form.vue';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '角色名称',
    },
    {
      component: 'Select',
      fieldName: 'globalMark',
      label: '角色类型',
      componentProps: {
        options: [
          { label: '全局', value: 1 },
          { label: '组织', value: 0 },
        ],
      },
    },
    {
      component: 'Select',
      fieldName: 'enabled',
      label: '状态',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ],
  fieldMappingTime: [['createTime', ['createTimeStart', 'createTimeEnd'], 'x']],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions<AppInfo> = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'name', title: '角色名称' },
    { field: 'code', title: '角色编码' },
    { field: 'globalMark', title: '角色类型', formatter: ['formatBoolean', { true: '全局', false: '组织' }] },
    { field: 'organFullNameList', title: '所属组织' },
    { field: 'createTime', title: '创建时间', formatter: 'formatDateTime' },
    { field: 'sortCode', title: '排序' },
    {
      field: 'enabled',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getRolePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const edit = (row: RoleInfo) => {
  modalTitle.value = $t('base.edit');
  modalApi.setData(row).open();
};
const roleFormRef = ref();
const loading = ref({
  save: false,
});
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    const formInfo = await roleFormRef.value.onSubmit();
    let api = addRoleApi;
    if (formInfo.id) {
      api = editRoleApi;
    }
    try {
      loading.value.save = true;
      await api(formInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } finally {
      loading.value.save = false;
    }
  },
  onOpened() {
    const value = modalApi.getData<Record<string, any>>();
    roleFormRef.value.init(value);
  },
});
const modalTitle = ref($t('base.add'));
const addRole = () => {
  modalTitle.value = $t('base.add');
  modalApi.setData(null).open();
};
const del = async (row: RoleInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await deleteRoleApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
function handleMember(row: RoleInfo) {
  openMemberModal(true, { id: row.id, name: row.name, objectType: 'ROLE' });
}
const [registerMember, { openModal: openMemberModal }] = useModal();
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="addRole">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <Dropdown>
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="user" @click="handleMember(row)"> 角色成员 </MenuItem>
                <MenuItem key="view"> 查看权限 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </Space>
      </template>
    </Grid>
    <Modal :title="modalTitle" :confirm-loading="loading.save">
      <RoleForm ref="roleFormRef" />
    </Modal>
    <Member @register="registerMember" />
  </Page>
</template>

<style></style>
