<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AppInfo } from '#/api';

import { ref } from 'vue';

import { IconPicker, Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { useElementSize } from '@vueuse/core';
import {
  Modal as antdModal,
  Button,
  Dropdown,
  Form,
  FormItem,
  Input,
  InputNumber,
  Menu,
  MenuItem,
  message,
  Select,
  SelectOption,
  Space,
  Switch,
  Textarea,
  TypographyLink,
} from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addAppApi, deleteAppApi, editAppApi, getAppPageListApi } from '#/api/app';

import MenuList from './menu-list.vue';

const pageRef = ref();
const { height } = useElementSize(pageRef);
const { getDictList } = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'code',
      label: '应用编码',
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '应用名称',
    },
    {
      component: 'Select',
      fieldName: 'enabled',
      label: '状态',
      componentProps: {
        options: getDictList('baseEnableType'),
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});

const gridOptions: VxeTableGridOptions<AppInfo> = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'code', title: '应用编码' },
    { field: 'name', title: '应用名称' },
    { field: 'description', title: '说明' },
    { field: 'icon', title: '图标', slots: { default: 'icon' } },
    { field: 'sortCode', title: '排序' },
    {
      field: 'enabled',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAppPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const loading = ref({
  app: false,
});
const appFormRef = ref();
const appForm = ref<Partial<AppInfo>>({});
const rules = {
  code: [{ required: true, message: '请输入应用编码' }],
  name: [{ required: true, message: '请输入应用名称' }],
  type: [{ required: true, message: '请选择应用类型' }],
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await appFormRef.value.validate();
    loading.value.app = true;
    let api = addAppApi;
    if (appForm.value.id) {
      api = editAppApi;
    }
    try {
      await api(appForm.value as AppInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } finally {
      loading.value.app = false;
    }
  },
  onClosed: () => {
    appForm.value = {};
    appFormRef.value.resetFields();
  },
});
const modalTitle = ref('新建应用');
const addApp = () => {
  modalTitle.value = '新建应用';
  modalApi.open();
};
const edit = (row: AppInfo) => {
  modalTitle.value = '编辑应用';
  modalApi.open();
  appForm.value = defaultsDeep(row, {});
};
const del = (row: AppInfo) => {
  antdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    onOk: async () => {
      await deleteAppApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const [registerMenuList, { openPopup: openMenuListPopup }] = usePopup();
const editAppMenu = (row: AppInfo) => {
  openMenuListPopup(true, row);
};
</script>

<template>
  <Page ref="pageRef" auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="addApp">新建</Button>
      </template>
      <template #icon="{ row }">
        <VbenIcon :icon="row.icon" class="text-lg" />
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink :disabled="Boolean(row.isMain)" type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <Dropdown>
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="menu" @click="editAppMenu(row)"> 菜单管理 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </Space>
      </template>
    </Grid>
    <Modal :confirm-loading="loading.app" class="w-[600px]" :title="modalTitle">
      <Form
        ref="appFormRef"
        :model="appForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :colon="false"
      >
        <FormItem label="应用编码" name="code">
          <Input v-model:value="appForm.code" />
        </FormItem>
        <FormItem label="应用名称" name="name">
          <Input v-model:value="appForm.name" />
        </FormItem>
        <FormItem label="应用类型" name="type">
          <Select v-model:value="appForm.type">
            <SelectOption value="PLATFORM">平台应用</SelectOption>
            <SelectOption value="CUSTOMER">客户应用</SelectOption>
          </Select>
        </FormItem>
        <FormItem label="图标" name="icon">
          <IconPicker v-model="appForm.icon" />
        </FormItem>
        <FormItem label="应用地址" name="url">
          <Input v-model:value="appForm.url" />
        </FormItem>
        <FormItem label="应用排序" name="sortCode">
          <InputNumber v-model:value="appForm.sortCode" :min="0" class="w-full" />
        </FormItem>
        <FormItem label="状态" name="enabled" initial-value="1">
          <Switch v-model:checked="appForm.enabled" :checked-value="1" :un-checked-value="0" />
        </FormItem>
        <FormItem label="说明" name="description">
          <Textarea v-model="appForm.description" :rows="3" />
        </FormItem>
      </Form>
    </Modal>
    <MenuList :height="height" @register="registerMenuList" />
  </Page>
</template>

<style></style>
