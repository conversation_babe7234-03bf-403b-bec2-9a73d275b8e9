/**
 * 增强下载功能使用示例
 * 
 * 这个文件展示了如何在实际项目中使用新的下载功能，
 * 优先从 Content-Disposition 响应头解析文件名
 */

import { requestClient } from '#/api/request';
import { downloadFileApi } from '#/api';

/**
 * 改进的云盘下载函数
 * 原来的实现比较粗暴，现在支持从 Content-Disposition 解析文件名
 */
export async function improvedCloudDiskDownload(selectedRowKeys: string[]) {
  try {
    // 获取下载链接
    const res = await downloadFileApi(selectedRowKeys);
    
    // 使用新的下载功能，优先从响应头解析文件名
    await requestClient.downloadAndSave(res.url, {
      fallbackFileName: res.name, // 使用 API 返回的名称作为备用
    });
    
    console.log('文件下载成功');
  } catch (error) {
    console.error('下载失败:', error);
    throw error;
  }
}

/**
 * 获取文件信息而不立即下载
 */
export async function getDownloadFileInfo(selectedRowKeys: string[]) {
  try {
    const res = await downloadFileApi(selectedRowKeys);
    
    // 获取文件信息
    const fileInfo = await requestClient.downloadWithFileName(res.url, {
      fallbackFileName: res.name,
    });
    
    return {
      fileName: fileInfo.fileName,
      fileSize: fileInfo.blob.size,
      fileType: fileInfo.blob.type,
      downloadUrl: res.url,
    };
  } catch (error) {
    console.error('获取文件信息失败:', error);
    throw error;
  }
}

/**
 * 批量下载多个文件
 */
export async function batchDownloadFiles(fileIdsList: string[][]) {
  const results = [];
  
  for (const fileIds of fileIdsList) {
    try {
      const res = await downloadFileApi(fileIds);
      
      await requestClient.downloadAndSave(res.url, {
        fallbackFileName: res.name,
      });
      
      results.push({
        fileIds,
        fileName: res.name,
        success: true,
      });
      
      console.log(`下载成功: ${res.name}`);
    } catch (error) {
      results.push({
        fileIds,
        fileName: null,
        success: false,
        error: error.message,
      });
      
      console.error(`下载失败:`, error);
    }
  }
  
  return results;
}

/**
 * 带进度监控的下载
 */
export async function downloadWithProgress(
  selectedRowKeys: string[],
  onProgress?: (loaded: number, total: number, percentage: number) => void
) {
  try {
    const res = await downloadFileApi(selectedRowKeys);
    
    const result = await requestClient.downloadWithFileName(res.url, {
      fallbackFileName: res.name,
      config: {
        onDownloadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(progressEvent.loaded, progressEvent.total, percentage);
          }
        },
      },
    });
    
    // 手动触发下载
    const downloadUrl = URL.createObjectURL(result.blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = result.fileName;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 清理临时 URL
    setTimeout(() => URL.revokeObjectURL(downloadUrl), 100);
    
    return result;
  } catch (error) {
    console.error('带进度的下载失败:', error);
    throw error;
  }
}

/**
 * Vue 组合式函数示例
 */
export function useEnhancedDownload() {
  const isDownloading = ref(false);
  const downloadProgress = ref(0);
  
  const downloadFile = async (selectedRowKeys: string[]) => {
    isDownloading.value = true;
    downloadProgress.value = 0;
    
    try {
      await downloadWithProgress(
        selectedRowKeys,
        (loaded, total, percentage) => {
          downloadProgress.value = percentage;
        }
      );
    } finally {
      isDownloading.value = false;
      downloadProgress.value = 0;
    }
  };
  
  const downloadMultipleFiles = async (fileIdsList: string[][]) => {
    isDownloading.value = true;
    
    try {
      const results = await batchDownloadFiles(fileIdsList);
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;
      
      console.log(`批量下载完成: ${successCount}/${totalCount} 个文件下载成功`);
      
      return results;
    } finally {
      isDownloading.value = false;
    }
  };
  
  return {
    isDownloading: readonly(isDownloading),
    downloadProgress: readonly(downloadProgress),
    downloadFile,
    downloadMultipleFiles,
  };
}

/**
 * 在 Vue 组件中的使用示例
 */
export const downloadUsageExample = {
  setup() {
    const { isDownloading, downloadProgress, downloadFile } = useEnhancedDownload();
    const selectedRowKeys = ref<string[]>([]);
    
    // 改进的下载处理函数
    const handleDownload = async () => {
      if (selectedRowKeys.value.length === 0) {
        console.warn('请选择要下载的文件');
        return;
      }
      
      try {
        await downloadFile(selectedRowKeys.value);
        console.log('下载完成');
      } catch (error) {
        console.error('下载失败:', error);
      }
    };
    
    return {
      isDownloading,
      downloadProgress,
      selectedRowKeys,
      handleDownload,
    };
  },
};

// 导出所有功能
export {
  improvedCloudDiskDownload as handleDownload, // 可以直接替换原来的 handleDownload
};
