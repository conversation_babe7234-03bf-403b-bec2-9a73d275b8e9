import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface OutputInvoicePageParams extends BaseDataParams {
  /**
   * 当前页
   */
  current?: string;
  /**
   * 每页的数量
   */
  size?: string;
  /**
   * 正排序规则
   */
  ascs?: string;
  /**
   * 倒排序规则
   */
  descs?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人ID
   */
  createBy?: string;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 修改人ID
   */
  updateBy?: string;
  /**
   * 标记删除
   */
  deleteFlag?: string;
  /**
   * 发票类型
   */
  invoiceType?: string;
  /**
   * 税务类型
   */
  taxType?: string;
  /**
   * 购方社会统一信用代码
   */
  buyerCompanyCode?: string;
  /**
   * 购买方公司名称
   */
  buyerCompanyName?: string;
  /**
   * 销方社会统一信用代码
   */
  sellerCompanyCode?: string;
  /**
   * 销售方公司名称
   */
  sellerCompanyName?: string;
  /**
   * 购买方公司地址
   */
  buyerCompanyAddress?: string;
  /**
   * 购买方联系方式
   */
  buyerCompanyContract?: string;
  /**
   * 销售方公司地址
   */
  sellerCompanyAddress?: string;
  /**
   * 销售方联系方式
   */
  sellerCompanyContract?: string;
  /**
   * 不含税总金额
   */
  totalAmount?: string;
  /**
   * 含税总金额
   */
  totalAmountTax?: string;
  /**
   * 状态
   */
  status?: string;
  /**
   * 发票代码
   */
  invoiceCode?: string;
  /**
   * 发票号码
   */
  invoiceNumber?: string;
  /**
   * 是否红冲（0：否，1：是）
   */
  isHc?: string;
  /**
   * 红冲原因
   */
  hcDesc?: string;
  /**
   * 原发票代码
   */
  sourceInvoiceCode?: string;
  /**
   * 原发票号码
   */
  sourceInvoiceNumber?: string;
  /**
   * 发票备注
   */
  remark?: string;
  /**
   * 发票日期
   */
  invoiceDate?: string;
  /**
   * 文件ID
   */
  fileId?: string;
  /**
   * 校验码后6位
   */
  checkCode?: string;
  /**
   * 版本号
   */
  version?: string;
}

export interface OutputInvoice extends BaseDataParams {
  /**
   * 主键
   */
  id?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人ID
   */
  createBy?: number;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 修改人ID
   */
  updateBy?: number;
  /**
   * 标记删除
   */
  deleteFlag?: boolean;
  /**
   * 发票类型
   */
  invoiceType?: string;
  /**
   * 税务类型
   */
  taxType?: string;
  /**
   * 购方社会统一信用代码
   */
  buyerCompanyCode?: string;
  /**
   * 购买方公司名称
   */
  buyerCompanyName?: string;
  /**
   * 销方社会统一信用代码
   */
  sellerCompanyCode?: string;
  /**
   * 销售方公司名称
   */
  sellerCompanyName?: string;
  /**
   * 购买方公司地址
   */
  buyerCompanyAddress?: string;
  /**
   * 购买方联系方式
   */
  buyerCompanyContract?: string;
  /**
   * 销售方公司地址
   */
  sellerCompanyAddress?: string;
  /**
   * 销售方联系方式
   */
  sellerCompanyContract?: string;
  /**
   * 不含税总金额
   */
  totalAmount?: number;
  /**
   * 含税总金额
   */
  totalAmountTax?: number;
  /**
   * 状态
   */
  status?: string;
  /**
   * 发票代码
   */
  invoiceCode?: string;
  /**
   * 发票号码
   */
  invoiceNumber?: string;
  /**
   * 是否红冲（0：否，1：是）
   */
  isHc?: string;
  /**
   * 红冲原因
   */
  hcDesc?: string;
  /**
   * 原发票代码
   */
  sourceInvoiceCode?: string;
  /**
   * 原发票号码
   */
  sourceInvoiceNumber?: string;
  /**
   * 发票备注
   */
  remark?: string;
  /**
   * 发票日期
   */
  invoiceDate?: string;
  /**
   * 文件ID
   */
  fileId?: string;
  /**
   * 校验码后6位
   */
  checkCode?: string;
  /**
   * 版本号
   */
  version?: number;
  outputInvoiceItemBOList: OutputInvoiceItemBO[];
}

export interface OutputInvoiceItemBO extends BaseDataParams {
  /**
   * 主键
   */
  id?: number;
  /**
   * 创建人
   */
  createBy?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 更新人
   */
  updateBy?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 逻辑删除
   */
  deleteFlag?: number;
  /**
   * 开票id
   */
  invoiceId?: number;
  /**
   * 类目名称
   */
  itemName?: string;
  /**
   * 规格型号
   */
  specifications?: string;
  /**
   * 计量单位
   */
  measureUnit?: string;
  /**
   * 数量
   */
  quantity?: number;
  /**
   * 不含税单价
   */
  unitPrice?: number;
  /**
   * 不含税金额
   */
  totalAmount?: number;
  /**
   * 税率
   */
  taxRate?: number;
  /**
   * 税额
   */
  taxAmount?: number;
  /**
   * 价税合计
   */
  totalAmountTax?: number;
  /**
   * 版本号
   */
  version?: number;
}

// 销项税票列表分页查询
export async function outputInvoicePageApi(params: PageListParams) {
  return requestClient.get<OutputInvoicePageParams[]>('/scm/output/invoice/page', { params });
}

// 新增销项税票
export async function outputInvoiceAddApi(data: OutputInvoice) {
  return requestClient.post<OutputInvoice>('/scm/output/invoice/add', data);
}

// 销项税票删除
export async function outputInvoiceDeleteApi(id: string) {
  return requestClient.post(`/scm/output/invoice/delete/${id}`);
}

// 编辑销项税票
export async function outputInvoiceEditApi(data: OutputInvoice) {
  return requestClient.post<OutputInvoice>('/scm/output/invoice/edit', data);
}

// 销项税票详情查询
export async function outputInvoiceDetailApi(id: string) {
  return requestClient.get(`/scm/output/invoice/detail/${id}`);
}

// 销项税票提交
export async function outputInvoiceSubmitApi(id: string) {
  return requestClient.post(`/scm/output/invoice/submit/${id}`);
}

// 销项税票列表查询
export async function outputInvoiceListApi(params: PageListParams) {
  return requestClient.get<OutputInvoicePageParams[]>('/scm/output/invoice/list', { params });
}
