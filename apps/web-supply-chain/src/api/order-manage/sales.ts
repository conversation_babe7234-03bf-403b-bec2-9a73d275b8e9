import type { RequestClientConfig } from '@vben/request';
import type { PageListParams } from '@vben/types';

import type {OrderCompanyInfo, OrderInfo} from './purchase';

import { requestClient } from '#/api/request';


export interface QueryGoodsRequestSales extends OrderCompanyInfo {
  projectId?: number;
  orderList?: string[];
  businessStructure?: string;
  isSnManaged?: boolean;
  warehouseId?: number;
}

export async function getSalesListApi(params: PageListParams) {
  return requestClient.get('/scm/order/sales/list', { params });
}
export async function addSalesListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/sales/save', data);
}
export async function editSalesListApi(data: OrderInfo) {
  return requestClient.post<OrderInfo>('/scm/order/sales/update', data);
}
export async function infoSalesListApi(params: OrderInfo) {
  return requestClient.get<OrderInfo>('/scm/order/sales/detail', { params });
}
export async function changeSalesApi(params: OrderInfo) {
  // CANCEL：作废   DRAFT：变更
  return requestClient.post<OrderInfo>('/scm/order/sales/change', params);
}
export async function delSalesApi(id: string) {
  return requestClient.post(`/scm/order/sales/delete?id=${id}`);
}
export async function importSalesApi(data: any, config: RequestClientConfig) {
  return requestClient.upload('/scm/order/sales/upload', data, config);
}
export async function downloadSalesTemplateApi() {
  return requestClient.downloadAndSave('/scm/order/sales/download');
}

// 出库商品查询
export async function getInventoryStockApi(params: QueryGoodsRequestSales) {
  return requestClient.post('/scm/inventory/stock/queryItemList', {params});
}
