<script setup lang="ts">
import type { InspectionInfo } from '#/api';

import { inject, ref } from 'vue';

import { FULL_FORM_ITEM_PROP } from '@vben/constants';

import BusinessTableForm from '#/views/operation/inspection/manage/components/business-table-form.vue';
import CheckTableForm from '#/views/operation/inspection/manage/components/check-table-form.vue';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

const inspectionForm = inject(inspectionFormKey, ref<Partial<InspectionInfo>>({}));
const partnerFinancialsData = ref({
  data: [],
  financialAnalysis: '',
});
const CheckTableFormRef = ref();
const BusinessTableFormRef = ref();
const init = () => {
  CheckTableFormRef.value.init();
  if (inspectionForm.value.partnerFinancialsData) {
    BusinessTableFormRef.value.init(JSON.parse(inspectionForm.value.partnerFinancialsData));
  }
};
const save = async () => {
  await CheckTableFormRef.value.save();
  partnerFinancialsData.value.data = await BusinessTableFormRef.value.save();
  return {
    partnerFinancialsData: JSON.stringify(partnerFinancialsData.value),
  };
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <CheckTableForm ref="CheckTableFormRef" key-prefix="partner" title="合作企业投后管理情况" />
    <BusinessTableForm ref="BusinessTableFormRef" />
    <a-form
      v-bind="{
        colon: false,
        ...FULL_FORM_ITEM_PROP,
      }"
    >
      <a-form-item label="财务分析">
        <a-textarea v-model:value="partnerFinancialsData.financialAnalysis" :rows="4" />
      </a-form-item>
    </a-form>
  </div>
</template>

<style></style>
