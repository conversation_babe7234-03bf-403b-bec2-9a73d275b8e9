<script setup lang="ts">
import type { PropType } from 'vue';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InspectionInfo } from '#/api';

import { inject, ref } from 'vue';

import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { useDictStore } from '@vben/stores';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

const props = defineProps({
  keyPrefix: { type: String as PropType<'cg' | 'partner'>, required: true },
  title: { type: String, default: '' },
});
const dictState = useDictStore();
const inspectionForm = inject(inspectionFormKey, ref<Partial<InspectionInfo>>({}));
interface DataItem {
  type: string;
  typeDesc: string;
  key: keyof InspectionInfo;
  status?: boolean;
  reason?: string;
}
const checkData: DataItem[] = [
  { type: '基本情况', typeDesc: '高管人员有无变化', key: `${props.keyPrefix}ExecChanged` },
  { type: '基本情况', typeDesc: '注册资本有无变化', key: `${props.keyPrefix}CapitalChanged` },
  { type: '基本情况', typeDesc: '股权结构有无变化', key: `${props.keyPrefix}EquityChanged` },
  { type: '基本情况', typeDesc: '经营范围有无变化', key: `${props.keyPrefix}ScopeChanged` },
  { type: '基本情况', typeDesc: '行政处罚有无变化', key: `${props.keyPrefix}PenaltyChanged` },
  { type: '基本情况', typeDesc: '司法纠纷有无变化', key: `${props.keyPrefix}LegalChanged` },
  { type: '基本情况', typeDesc: '银行征信有无变化', key: `${props.keyPrefix}CreditChanged` },
  { type: '基本情况', typeDesc: '是否存在负面舆情', key: `${props.keyPrefix}NegativeNews` },
  { type: '对外融资', typeDesc: '是否新增银行融资', key: `${props.keyPrefix}BankLoanAdded` },
  { type: '对外融资', typeDesc: '是否新增非标融资', key: `${props.keyPrefix}NonStdLoanAdded` },
  { type: '对外融资', typeDesc: '是否新增股权融资', key: `${props.keyPrefix}EquityLoanAdded` },
  { type: '对外融资', typeDesc: '是否新增债券融资', key: `${props.keyPrefix}BondLoanAdded` },
  { type: '担保情况', typeDesc: '是否新增担保', key: `${props.keyPrefix}GuaranteeAdded` },
];
const checkColumns = [
  { title: '检查类型', field: 'type' },
  { title: '检查内容', field: 'typeDesc' },
  { title: '状态', field: 'status', slots: { default: 'status' } },
  { title: '具体情况', field: 'reason', slots: { default: 'reason' } },
];
const checkGridOptions: VxeTableGridOptions = {
  data: checkData,
  mergeCells: [
    { row: 0, col: 0, rowspan: 8, colspan: 1 },
    { row: 8, col: 0, rowspan: 4, colspan: 1 },
  ],
  columns: checkColumns,
  ...DETAIL_GRID_OPTIONS,
};
const [CheckGrid, checkGridApi] = useVbenVxeGrid({
  gridOptions: checkGridOptions,
});
const init = () => {
  checkData.forEach((item: DataItem) => {
    item.status = inspectionForm.value[item.key];
    item.reason = inspectionForm.value[`${item.key}Details` as keyof InspectionInfo];
  });
  checkGridApi.grid.reloadData(checkData);
};
const save = async () => {
  const { visibleData } = checkGridApi.grid.getTableData();
  visibleData.forEach((item: DataItem) => {
    inspectionForm.value[item.key] = item.status;
    inspectionForm.value[`${item.key}Details` as keyof InspectionInfo] = item.reason;
  });
  return inspectionForm.value;
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <CheckGrid :table-title="title">
      <template #status="{ row }">
        <a-radio-group v-model:value="row.status">
          <a-radio v-for="dict in dictState.getDictList('baseBooleanType')" :key="dict.value" :value="dict.value">
            {{ dict.label }}
          </a-radio>
        </a-radio-group>
      </template>
      <template #reason="{ row }">
        <a-input v-model:value="row.reason" />
      </template>
    </CheckGrid>
  </div>
</template>

<style></style>
