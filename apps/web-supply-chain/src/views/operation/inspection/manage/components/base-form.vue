<script setup lang="ts">
import type { InitiationBaseInfo, InspectionInfo } from '#/api';

import { computed, inject, reactive, ref } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';

import { getProjectListApi } from '#/api';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

const colSpan = COL_SPAN_PROP;
const formProp = FORM_PROP;
const fullProp = FULL_FORM_ITEM_PROP;
const inspectionForm = inject(inspectionFormKey, ref<Partial<InspectionInfo>>({}));
const rules = {
  reportName: [{ required: true, message: '请输入检查报告名称' }],
  projectId: [{ required: true, message: '请选择项目名称', trigger: 'change' }],
};
const BaseFormRef = ref();
const state = reactive({
  projectList: [],
});
const init = () => {};
const save = async () => {
  await BaseFormRef.value.validate();
  return inspectionForm.value;
};
const getProjectList = async () => {
  state.projectList = await getProjectListApi({});
};
getProjectList();
const handleChangeProject = (_value: number, option?: InitiationBaseInfo) => {
  if (option) {
    inspectionForm.value.projectName = option.projectName;
    inspectionForm.value.inspectionOfficers = option.businessManagerName;
    inspectionForm.value.projectType = option.projectModel;
    inspectionForm.value.creditLimit = option.expectedProjectScale;
    inspectionForm.value.serviceFeeRate = option.serviceFeeRate;
    inspectionForm.value.paymentTerm = option.paymentTermDays;
  }
};
const inspectionDate = computed({
  get() {
    return inspectionForm.value.inspectionDate?.toString();
  },
  set(newValue: string) {
    inspectionForm.value.inspectionDate = newValue ? Number(newValue) : undefined;
  },
});
defineExpose({ init, save });
</script>

<template>
  <a-form ref="BaseFormRef" :model="inspectionForm" :rules="rules" v-bind="formProp">
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="检查报告名称" name="reportName">
          <a-input v-model:value="inspectionForm.reportName" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="项目名称" name="projectId">
          <a-select
            v-model:value="inspectionForm.projectId"
            :options="state.projectList"
            :field-names="{ label: 'projectName', value: 'id' }"
            @change="handleChangeProject"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="检查人员" name="inspectionOfficers">
          <a-input v-model:value="inspectionForm.inspectionOfficers" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="检查方式" name="inspectionMethod">
          <a-input v-model:value="inspectionForm.inspectionMethod" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="检查日期" name="inspectionDate">
          <a-date-picker v-model:value="inspectionDate" value-format="x" class="w-full" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="项目类型" name="projectType">
          <StatusTag :value="inspectionForm.projectType" code="PROJECT_MODE" />
          <!--<a-input v-model:value="inspectionForm.projectType" />-->
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="合作额度" name="creditLimit">
          <a-input v-model:value="inspectionForm.creditLimit" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="累计投放金额" name="cumulativeLendingAmount">
          <a-input v-model:value="inspectionForm.cumulativeLendingAmount" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="存量额度" name="outstandingBalance">
          <a-input v-model:value="inspectionForm.outstandingBalance" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="服务费率" name="serviceFeeRate">
          <a-input v-model:value="inspectionForm.serviceFeeRate" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="账期" name="paymentTerm">
          <a-input v-model:value="inspectionForm.paymentTerm" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="增信措施" name="creditEnhancementMeasures">
          <a-input v-model:value="inspectionForm.creditEnhancementMeasures" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="核心企业" name="coreEnterpriseName">
          <a-input v-model:value="inspectionForm.coreEnterpriseName" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="合作企业" name="partnerEnterpriseName">
          <a-input v-model:value="inspectionForm.partnerEnterpriseName" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="增信措施落实情况" name="creditEnhancementImplementation" v-bind="fullProp">
          <a-textarea v-model:value="inspectionForm.creditEnhancementImplementation" :rows="4" />
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>

<style></style>
