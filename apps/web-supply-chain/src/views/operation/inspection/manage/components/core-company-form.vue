<script setup lang="ts">
import type { InspectionInfo } from '#/api';

import { inject, ref } from 'vue';

import { FULL_FORM_ITEM_PROP } from '@vben/constants';

import BusinessTableForm from '#/views/operation/inspection/manage/components/business-table-form.vue';
import CheckTableForm from '#/views/operation/inspection/manage/components/check-table-form.vue';
import EnergyConsumptionTableForm from '#/views/operation/inspection/manage/components/energy-consumption-table-form.vue';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

defineProps({ title: { type: String, default: '核心企业投后管理情况' } });
const inspectionForm = inject(inspectionFormKey, ref<Partial<InspectionInfo>>({}));
const CheckTableFormRef = ref();
const EnergyConsumptionTableFormRef = ref();
const BusinessTableFormRef = ref();
const cgFinancialsData = ref({
  data: [],
  financialAnalysis: '',
});
const init = () => {
  CheckTableFormRef.value.init();
  if (inspectionForm.value.projectType === 'INDUSTRY') {
    EnergyConsumptionTableFormRef.value.init();
  }
  if (inspectionForm.value.cgFinancialsData) {
    cgFinancialsData.value = JSON.parse(inspectionForm.value.cgFinancialsData);
    BusinessTableFormRef.value.init(cgFinancialsData.value.data);
  }
};
const save = async () => {
  await CheckTableFormRef.value.save();
  cgFinancialsData.value.data = await BusinessTableFormRef.value.save();
  const formData: { cgFinancialsData: string; energyConsumptionData?: string } = {
    cgFinancialsData: JSON.stringify(cgFinancialsData.value),
  };
  if (inspectionForm.value.projectType === 'INDUSTRY') {
    const energyConsumptionData = await EnergyConsumptionTableFormRef.value.save();
    formData.energyConsumptionData = JSON.stringify(energyConsumptionData);
  }
  return formData;
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <CheckTableForm ref="CheckTableFormRef" key-prefix="cg" :title="title" />
    <EnergyConsumptionTableForm ref="EnergyConsumptionTableFormRef" v-if="inspectionForm.projectType === 'INDUSTRY'" />
    <a-form
      v-bind="{
        colon: false,
        ...FULL_FORM_ITEM_PROP,
      }"
    >
      <a-form-item v-if="inspectionForm.projectType === 'INDUSTRY'" label="情况描述">
        <a-textarea v-model:value="inspectionForm.cgEnterpriseDetails" :rows="4" />
      </a-form-item>
      <BusinessTableForm ref="BusinessTableFormRef" />
      <a-form-item label="财务分析">
        <a-textarea v-model:value="cgFinancialsData.financialAnalysis" :rows="4" />
      </a-form-item>
    </a-form>
  </div>
</template>

<style></style>
