<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { InspectionInfo } from '#/api';

import { inject, ref } from 'vue';

import { confirm } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

const inspectionForm = inject(inspectionFormKey, ref<Partial<InspectionInfo>>({}));
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { title: '时间', field: 'date', slots: { default: 'edit_column' } },
    {
      title: '能耗情况',
      align: 'center',
      children: [
        { title: '电费', field: 'electricity', slots: { default: 'edit_column' } },
        { title: '水费', field: 'water', slots: { default: 'edit_column' } },
        { title: '燃气费', field: 'gas', slots: { default: 'edit_column' } },
        { title: '物流费', field: 'logistics', slots: { default: 'edit_column' } },
      ],
    },
  ],
  ...DETAIL_GRID_OPTIONS,
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const addRow = () => {
  gridApi.grid.insertAt({});
};
const removeRow = async () => {
  const selectedRows = gridApi.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.error('请选择要删除的数据');
    return;
  }
  await confirm('确认删除此数据？', '确认删除');
  await gridApi.grid.remove(selectedRows);
};
const init = () => {
  if (inspectionForm.value.energyConsumption) {
    gridApi.grid.loadData(JSON.parse(inspectionForm.value.energyConsumption));
  }
};
const save = async () => {
  const { visibleData } = gridApi.grid.getTableData();
  visibleData.forEach((item) => {
    delete item._X_ROW_KEY;
  });
  return visibleData;
};
defineExpose({ init, save });
</script>

<template>
  <Grid table-title="水电费燃气物流费">
    <template #toolbar-tools>
      <a-space>
        <a-button type="primary" @click="addRow">新增</a-button>
        <a-button type="primary" danger @click="removeRow">删除</a-button>
      </a-space>
    </template>
    <template #edit_column="{ row, column }">
      <a-input v-model:value="row[column.field]" />
    </template>
  </Grid>
</template>

<style></style>
