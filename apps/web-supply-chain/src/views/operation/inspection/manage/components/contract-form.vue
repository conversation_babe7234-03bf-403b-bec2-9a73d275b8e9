<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { InspectionInfo } from '#/api';

import { inject, ref } from 'vue';

import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { useDictStore } from '@vben/stores';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

const inspectionForm = inject(inspectionFormKey, ref<Partial<InspectionInfo>>({}));
const baseColumns = [
  { title: '合作客户', field: 'type' },
  { title: '评价维度', field: 'evaluate' },
  { title: '具体表现', field: 'detail' },
  { title: '状态', field: 'status', slots: { default: 'status' } },
  { title: '具体情况', field: 'reason', slots: { default: 'reason' } },
];
interface DataItem {
  type: string;
  evaluate: string;
  detail: string;
  key: keyof InspectionInfo;
  status?: boolean;
  reason?: string;
}
const data: DataItem[] = [
  { type: '上游供应商', evaluate: '发货交货情况', detail: '发货是否及时', key: 'contractSupplierDelivOntime' },
  { type: '上游供应商', evaluate: '发货交货情况', detail: '是否超发少发货物', key: 'contractSupplierDelivQtyIssue' },
  { type: '上游供应商', evaluate: '发货交货情况', detail: '是否错发货物', key: 'contractSupplierDelivWrongItem' },
  { type: '上游供应商', evaluate: '发货交货情况', detail: '是否按期到货', key: 'contractSupplierDelivArrivalOntime' },
  { type: '上游供应商', evaluate: '开票结算情况', detail: '开票是否及时', key: 'contractSupplierInvoiceOntime' },
  { type: '上游供应商', evaluate: '开票结算情况', detail: '是否存在开票差错', key: 'contractSupplierInvoiceError' },
  { type: '上游供应商', evaluate: '开票结算情况', detail: '是否定期对账结算', key: 'contractSupplierReconRegular' },
  { type: '下游客户', evaluate: '货物接受情况', detail: '是否临时变更采购计划', key: 'contractCustomerPlanChanged' },
  { type: '下游客户', evaluate: '货物接受情况', detail: '是否存在退换货', key: 'contractCustomerReturn' },
  { type: '下游客户', evaluate: '回款情况', detail: '是否按时回款', key: 'contractCustomerPaymentOntime' },
];
const gridOptions: VxeTableGridOptions = {
  data,
  mergeCells: [
    { row: 0, col: 0, rowspan: 7, colspan: 1 },
    { row: 7, col: 0, rowspan: 3, colspan: 1 },
    { row: 0, col: 1, rowspan: 4, colspan: 1 },
    { row: 4, col: 1, rowspan: 3, colspan: 1 },
    { row: 7, col: 1, rowspan: 2, colspan: 1 },
  ],
  columns: baseColumns,
  ...DETAIL_GRID_OPTIONS,
};
const dictState = useDictStore();
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const init = () => {
  data.forEach((item) => {
    item.status = inspectionForm.value[item.key];
    item.reason = inspectionForm.value[`${item.key}Details` as keyof InspectionInfo];
  });
  gridApi.grid.loadData(data);
};
const save = async () => {
  const { visibleData } = gridApi.grid.getTableData();
  visibleData.forEach((item: DataItem) => {
    inspectionForm.value[item.key] = item.status;
    inspectionForm.value[`${item.key}Details` as keyof InspectionInfo] = item.reason;
  });
  return inspectionForm.value;
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <Grid table-title="合同履约情况">
      <template #status="{ row }">
        <a-radio-group v-model:value="row.status">
          <a-radio v-for="dict in dictState.getDictList('baseBooleanType')" :key="dict.value" :value="dict.value">
            {{ dict.label }}
          </a-radio>
        </a-radio-group>
      </template>
      <template #reason="{ row }">
        <a-input v-model:value="row.reason" />
      </template>
    </Grid>
  </div>
</template>

<style></style>
