<script setup lang="ts">
import type { VxeGridProps, VxeGridPropTypes } from 'vxe-table';

import { computed, ref } from 'vue';

import { confirm, prompt } from '@vben/common-ui';
import { COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import { VxeGrid } from 'vxe-table';

interface Props {
  companyType: 'down' | 'supplier' | 'warehouse';
}
const props = defineProps<Props>();

const dictState = useDictStore();
const companyTypeName = computed(() => {
  switch (props.companyType) {
    case 'down': {
      return '下游客户';
    }
    case 'supplier': {
      return '供应商';
    }
    case 'warehouse': {
      return '仓库';
    }
    default: {
      return '未知';
    }
  }
});

const checkColumns: VxeGridPropTypes.Columns = [
  { title: '企业名称', field: 'companyName' },
  { title: '检查项', field: 'type' },
  { title: '检查内容', field: 'typeDesc' },
  { title: '状态', field: 'status', slots: { default: 'status' } },
  { title: '具体情况', field: 'detail', slots: { default: 'detail' } },
];

const companyMergeCells = [
  { row: 0, col: 0, rowspan: 10, colspan: 1 },
  { row: 0, col: 1, rowspan: 8, colspan: 1 },
  { row: 8, col: 1, rowspan: 2, colspan: 1 },
];
const warehouseMergeCells = [
  { row: 0, col: 0, rowspan: 9, colspan: 1 },
  { row: 0, col: 1, rowspan: 8, colspan: 1 },
  { row: 8, col: 3, rowspan: 1, colspan: 2 },
  // { row: 8, col: 4, rowspan: 1, colspan: 2 },
];
const mergeCells = computed(() => {
  console.log(props.companyType === 'warehouse' ? warehouseMergeCells : companyMergeCells);
  return props.companyType === 'warehouse' ? warehouseMergeCells : companyMergeCells;
});
const checkGridOptions: VxeGridProps = {
  toolbarConfig: {
    refresh: false,
    custom: false,
    zoom: false,
    slots: {
      buttons: 'toolbarButtons',
    },
  },
};

interface DataItem {
  companyName?: string;
  type: string;
  typeDesc: string;
  status?: 0 | 1;
  detail?: string;
  [key: string]: any;
}

const baseCheckData: DataItem[] = [
  { type: '基本情况', typeDesc: '高管人员有无变化' },
  { type: '基本情况', typeDesc: '注册资本有无变化' },
  { type: '基本情况', typeDesc: '股权结构有无变化' },
  { type: '基本情况', typeDesc: '经营范围有无变化' },
  { type: '基本情况', typeDesc: '行政处罚有无变化' },
  { type: '基本情况', typeDesc: '司法纠纷有无变化' },
  { type: '基本情况', typeDesc: '是否存在负面舆情' },
  { type: '基本情况', typeDesc: '是否新增银行融资' },
];
const contractCheckData: DataItem[] = [
  { type: '合同履行情况', typeDesc: '发货交货是否正常' },
  { type: '合同履行情况', typeDesc: '开票结算是否正常' },
];
const warehouseCheckData: DataItem[] = [{ type: '仓库运营情况', typeDesc: '盘点及日常合作情况' }];

const businessGridOptions: VxeGridProps = {
  toolbarConfig: {
    refresh: false,
    custom: false,
    zoom: false,
    slots: {
      buttons: 'toolbarButtons',
    },
  },
};

interface CompanyItem {
  companyName: string;
  checkData: DataItem[];
  businessData?: {
    data: Record<string, any>[];
    titleMonth1?: string;
    titleMonth2?: string;
    titleMonth3?: string;
  };
  stockBalance?: string;
  stockGoodsQuantity?: string;
  warehouseGoodsQuery?: string;
  warehouseGoodsAmount?: string;
}
const companyData = ref<CompanyItem[]>([]);
const businessColumns = ref<VxeGridPropTypes.Columns[]>([]);

const addCompany = async () => {
  const companyName = await prompt({
    content: '请输入企业名称',
    title: '新增企业',
    beforeClose(scope) {
      if (scope.isConfirm && !scope.value) {
        message.error('企业名称不能为空');
        return false;
      }
    },
  });
  if (companyName) {
    const checkData = [];
    if (props.companyType === 'warehouse') {
      checkData.push(...baseCheckData, ...warehouseCheckData);
    } else {
      checkData.push(...baseCheckData, ...contractCheckData);
    }
    const companyInitData: CompanyItem = {
      companyName,
      checkData: checkData.map((item) => ({ ...item, detail: '', companyName })),
    };
    if (props.companyType !== 'warehouse') {
      companyInitData.businessData = {
        titleMonth1: '',
        titleMonth2: '',
        titleMonth3: '',
        data: [{}],
      };
    }
    companyData.value.push(companyInitData);
    if (props.companyType !== 'warehouse') {
      businessColumns.value.push([
        { title: '', field: 'data0', slots: { header: 'data_header', default: 'data_edit' } },
        { title: '', field: 'data1', slots: { header: 'data_header', default: 'data_edit' } },
        { title: '', field: 'data2', slots: { header: 'data_header', default: 'data_edit' } },
      ]);
    }
    console.log(companyData.value);
  }
};
const removeCompany = async (index: number) => {
  await confirm({ content: '确定要删除此企业？', title: '删除企业' });
  companyData.value.splice(index, 1);
  businessColumns.value.splice(index, 1);
};
const BusinessTableRef = ref();
const init = (list: string) => {
  if (list) {
    companyData.value = JSON.parse(list);
  }
};
const save = () => {
  console.log(companyData.value);
  const companyInfo = companyData.value;
  companyInfo.forEach((item) => {
    item.checkData.forEach((checkItem: DataItem) => {
      delete checkItem._X_ROW_KEY;
    });
    if (item.businessData) {
      item.businessData.data.forEach((businessItem) => {
        delete businessItem._X_ROW_KEY;
      });
    }
  });
  return {
    companyList: JSON.stringify(companyInfo),
  };
};
/**
 * 创建一个用于绑定 companyData 数组中特定项的计算属性
 * @param index companyData 数组的索引
 * @param key 要绑定的属性名
 * @param defaultValue 如果属性不存在时的默认值
 */
const getCompanyDataItem = (index: number, key: keyof CompanyItem, defaultValue: any = '') => {
  return computed({
    get() {
      return (companyData.value[index]?.[key] as any) ?? defaultValue;
    },
    set(newValue: any) {
      if (companyData.value[index]) {
        (companyData.value[index] as any)[key] = newValue; // 使用类型断言
      }
    },
  });
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <BasicCaption :content="`${companyTypeName}情况`">
      <template #action>
        <a-button type="primary" @click="addCompany">新增</a-button>
      </template>
    </BasicCaption>
    <div v-for="(item, index) in companyData" :key="`company-${props.companyType}-${index}`">
      <VxeGrid :columns="checkColumns" :data="item.checkData" :merge-cells="mergeCells" v-bind="checkGridOptions">
        <template #toolbarButtons>
          <a-button type="primary" danger @click="removeCompany(index)">删除</a-button>
        </template>
        <template #status="{ row }: { row: DataItem }">
          <a-input v-if="row.typeDesc === '盘点及日常合作情况'" v-model:value="row.detail" />
          <a-radio-group v-else v-model:value="row.status">
            <a-radio v-for="dict in dictState.getDictList('baseBooleanType')" :key="dict.value" :value="dict.value">
              {{ dict.label }}
            </a-radio>
          </a-radio-group>
        </template>
        <template #detail="{ row }: { row: DataItem }">
          <a-input v-model:value="row.detail" />
        </template>
      </VxeGrid>
      <VxeGrid
        v-if="props.companyType !== 'warehouse'"
        ref="BusinessTableRef"
        :columns="businessColumns[index]"
        :data="item.businessData!.data"
        v-bind="businessGridOptions"
      >
        <template #toolbarButtons>
          <div class="mr-1 pl-1 text-[1rem]">采购销售情况（万元）</div>
        </template>
        <template #data_header="{ columnIndex }: { columnIndex: number }">
          <a-input
            v-model:value="item.businessData![`titleMonth${columnIndex + 1}` as keyof CompanyItem['businessData']]"
            class="w-full"
          />
        </template>
        <template #data_edit="{ row, column }">
          <a-input v-model:value="row[column.field]" />
        </template>
      </VxeGrid>
      <a-form v-if="props.companyType === 'down'" v-bind="FORM_PROP" class="mt-4">
        <a-row>
          <a-col v-bind="COL_SPAN_PROP">
            <a-form-item label="存量余额">
              <a-input v-model:value="getCompanyDataItem(index, 'stockBalance').value" />
            </a-form-item>
          </a-col>
          <a-col v-bind="COL_SPAN_PROP">
            <a-form-item label="在手货物数量">
              <a-input v-model:value="getCompanyDataItem(index, 'stockGoodsQuantity').value" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-form v-if="props.companyType === 'warehouse'" v-bind="FORM_PROP" class="mt-4">
        <a-row>
          <a-col v-bind="COL_SPAN_PROP">
            <a-form-item label="库内货物数量">
              <a-input v-model:value="getCompanyDataItem(index, 'warehouseGoodsQuery').value" />
            </a-form-item>
          </a-col>
          <a-col v-bind="COL_SPAN_PROP">
            <a-form-item label="库内货物金额">
              <a-input v-model:value="getCompanyDataItem(index, 'warehouseGoodsAmount').value" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </div>
</template>

<style></style>
