<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { confirm } from '@vben/common-ui';
import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

const businessGridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { title: '年份', field: 'year', slots: { default: 'edit_column' } },
    { title: '货币资金', field: 'monetaryFund', slots: { default: 'edit_column' } },
    { title: '总资产', field: 'totalAssets', slots: { default: 'edit_column' } },
    { title: '总负债', field: 'totalLiabilities', slots: { default: 'edit_column' } },
    { title: '所有者权益', field: 'ownersEquity', slots: { default: 'edit_column' } },
    { title: '营业收入', field: 'operatingRevenue', slots: { default: 'edit_column' } },
    { title: '营业成本', field: 'operatingCost', slots: { default: 'edit_column' } },
    { title: '利润总额', field: 'totalProfit', slots: { default: 'edit_column' } },
    { title: '净利润', field: 'netProfit', slots: { default: 'edit_column' } },
    { title: '经营性净现金流', field: 'netCashFlowFromOperatingActivities', slots: { default: 'edit_column' } },
    { title: '资产负债率', field: 'assetLiabilityRatio', slots: { default: 'edit_column' } },
    { title: '净资产收益率', field: 'returnOnEquity', slots: { default: 'edit_column' } },
    { title: '毛利率', field: 'grossProfitMargin', slots: { default: 'edit_column' } },
    { title: '净利率', field: 'netProfitMargin', slots: { default: 'edit_column' } },
  ],
  ...DETAIL_GRID_OPTIONS,
};
const [BusinessGrid, businessGridApi] = useVbenVxeGrid({
  gridOptions: businessGridOptions,
});
const addBusinessRow = () => {
  businessGridApi.grid.insertAt({});
};
const removeBusinessRow = async () => {
  const selectedRows = businessGridApi.grid.getCheckboxRecords();
  if (selectedRows.length === 0) {
    message.error('请选择要删除的数据');
    return;
  }
  await confirm('确认删除此数据？', '确认删除');
  await businessGridApi.grid.remove(selectedRows);
};
const init = (list: { [key: string]: { [key: string]: string } }[]) => {
  businessGridApi.grid.loadData(list);
};
const save = async () => {
  const { visibleData: businessData } = businessGridApi.grid.getTableData();
  const data = cloneDeep(businessData);
  data.forEach((item) => {
    delete item._X_ROW_KEY;
  });
  return data;
};
defineExpose({ init, save });
</script>

<template>
  <BusinessGrid>
    <template #toolbar-tools>
      <a-space>
        <a-button type="primary" @click="addBusinessRow">新增</a-button>
        <a-button type="primary" danger @click="removeBusinessRow">删除</a-button>
      </a-space>
    </template>
    <template #edit_column="{ row, column }">
      <a-input v-model:value="row[column.field]" />
    </template>
  </BusinessGrid>
</template>

<style></style>
