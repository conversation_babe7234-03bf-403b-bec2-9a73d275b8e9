<script setup lang="ts">
import type { InspectionInfo } from '#/api';

import { computed, provide, reactive, ref, unref } from 'vue';

import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  addBuildingInspectionApi,
  addIndustryInspectionApi,
  addWarehouseInspectionApi,
  getInspectionDetailApi,
} from '#/api';
import { SimpleUpload } from '#/components';
import BaseForm from '#/views/operation/inspection/manage/components/base-form.vue';
import CompanyListForm from '#/views/operation/inspection/manage/components/company-list-form.vue';
import ContractForm from '#/views/operation/inspection/manage/components/contract-form.vue';
import CoreCompanyForm from '#/views/operation/inspection/manage/components/core-company-form.vue';
import PartnerCompanyForm from '#/views/operation/inspection/manage/components/partner-company-form.vue';
import PurchaseSalesForm from '#/views/operation/inspection/manage/components/purchase-sales-form.vue';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

const emit = defineEmits(['ok', 'register']);
const colSpan = COL_SPAN_PROP;
const state = reactive<{ id?: number }>({
  id: undefined,
});
const pageTitle = computed(() => {
  return state.id ? '编辑' : '新增';
});
const BaseFormRef = ref();
const PurchaseSalesFormRef = ref();
const ContractFormRef = ref();
const PartnerCompanyListFormRef = ref();
const CoreCompanyFormRef = ref();
const SupplierCompanyListFormRef = ref();
const DownCompanyListFormRef = ref();
const WarehouseCompanyListFormRef = ref();
const coreCompanyTitle = computed(() => {
  return inspectionForm.value.projectType === 'INDUSTRY' ? '核心企业投后管理情况' : '担保企业投后管理情况';
});
const inspectionForm = ref<Partial<InspectionInfo>>({
  inspectionDate: Number(dayjs().startOf('day').format('x')),
  reportDate: Number(dayjs().startOf('day').format('x')),
});
const getDetail = async () => {
  if (state.id) {
    inspectionForm.value = await getInspectionDetailApi(state.id);
    if (inspectionForm.value.projectType && ['BUILDING', 'INDUSTRY'].includes(inspectionForm.value.projectType)) {
      ContractFormRef.value.init();
      PartnerCompanyListFormRef.value.init();
      CoreCompanyFormRef.value.init();
    } else if (inspectionForm.value.projectType && ['WAREHOUSE'].includes(inspectionForm.value.projectType)) {
      SupplierCompanyListFormRef.value.init(inspectionForm.value.wdPartnersData);
      DownCompanyListFormRef.value.init(inspectionForm.value.customerDataDetails);
      WarehouseCompanyListFormRef.value.init(inspectionForm.value.wdWarehousesData);
    }
  }
};
const init = (data: { id: number }) => {
  state.id = data.id;
  getDetail();
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const save = async () => {
  const formData = unref(inspectionForm);
  await BaseFormRef.value.save();
  if (formData.projectType && ['BUILDING', 'INDUSTRY'].includes(formData.projectType)) {
    const { purchaseSalesData } = await PurchaseSalesFormRef.value.save();
    await ContractFormRef.value.save();
    const { partnerFinancialsData } = await PartnerCompanyListFormRef.value.save();
    const { energyConsumptionData, cgFinancialsData } = await CoreCompanyFormRef.value.save();
    formData.purchaseSalesData = purchaseSalesData;
    formData.partnerFinancialsData = partnerFinancialsData;
    formData.energyConsumptionData = energyConsumptionData;
    formData.cgFinancialsData = cgFinancialsData;
  } else if (formData.projectType && ['WAREHOUSE'].includes(formData.projectType)) {
    const { companyList: wdPartnersData } = await SupplierCompanyListFormRef.value.save();
    const { companyList: customerDataDetails } = await DownCompanyListFormRef.value.save();
    const { companyList: wdWarehousesData } = await WarehouseCompanyListFormRef.value.save();
    formData.wdPartnersData = wdPartnersData;
    formData.customerDataDetails = customerDataDetails;
    formData.wdWarehousesData = wdWarehousesData;
  }
  changeOkLoading(true);
  try {
    let api = null;
    switch (formData.projectType) {
      case 'BUILDING': {
        api = addBuildingInspectionApi;
        break;
      }
      case 'INDUSTRY': {
        api = addIndustryInspectionApi;
        break;
      }
      case 'WAREHOUSE': {
        api = addWarehouseInspectionApi;
        break;
      }
    }
    if (!api) {
      throw new Error('不支持的项目类型');
    }
    await api(formData);
    message.success('保存成功');
    emit('ok');
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const reportDate = computed({
  get() {
    return inspectionForm.value.reportDate?.toString();
  },
  set(newValue: string) {
    inspectionForm.value.reportDate = newValue ? Number(newValue) : undefined;
  },
});

provide(inspectionFormKey, inspectionForm);
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="pageTitle" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <!-- 基本情况 -->
      <BaseForm ref="BaseFormRef" />
      <template v-show="inspectionForm.projectType && ['INDUSTRY', 'BUILDING'].includes(inspectionForm.projectType)">
        <!-- 采购销售情况 -->
        <PurchaseSalesForm ref="PurchaseSalesFormRef" />
        <!-- 合同履约情况 -->
        <ContractForm ref="ContractFormRef" />
        <!-- 合作企业投后管理情况 -->
        <PartnerCompanyForm ref="PartnerCompanyListFormRef" />
        <!-- 核心企业/担保企业投后管理情况 -->
        <CoreCompanyForm ref="CoreCompanyFormRef" :title="coreCompanyTitle" />
        <a-form v-bind="FORM_PROP">
          <BasicCaption content="抵质押物检查情况" class="mb-4" />
          <a-form-item label="存续情况" name="collateralStatus" v-bind="FULL_FORM_ITEM_PROP">
            <a-textarea v-model:value="inspectionForm.collateralStatus" :rows="4" />
          </a-form-item>
        </a-form>
      </template>
      <template v-show="inspectionForm.projectType && ['WAREHOUSE'].includes(inspectionForm.projectType)">
        <!-- 供应商情况 -->
        <CompanyListForm ref="SupplierCompanyListFormRef" company-type="supplier" />
        <!-- 下游客户情况 -->
        <CompanyListForm ref="DownCompanyListFormRef" company-type="down" />
        <!-- 仓库情况 -->
        <CompanyListForm ref="WarehouseCompanyListFormRef" company-type="warehouse" />
      </template>
      <a-form v-bind="FORM_PROP">
        <BasicCaption content="检查结果" class="mb-4" />
        <a-form-item label="其他情况" name="otherInfoSummary" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.otherInfoSummary" :rows="4" />
        </a-form-item>
        <a-form-item label="检查结论" name="conclusion" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.conclusion" :rows="4" />
        </a-form-item>
        <a-form-item label="资产分类" name="assetClassification" v-bind="FULL_FORM_ITEM_PROP">
          <a-textarea v-model:value="inspectionForm.assetClassification" :rows="4" />
        </a-form-item>
        <BasicCaption content="其他信息" class="mb-4" />
        <a-row>
          <a-col v-bind="colSpan">
            <a-form-item label="运营人员" name="operationsOfficer">
              <a-input v-model:value="inspectionForm.operationsOfficer" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="复核人员" name="reviewOfficer">
              <a-input v-model:value="inspectionForm.reviewOfficer" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="报告日期" name="reportDate">
              <a-date-picker v-model:value="reportDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="底稿附件" name="draftAttachment" v-bind="FULL_FORM_ITEM_PROP">
              <a-textarea v-model:value="inspectionForm.draftAttachment" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <BasicCaption content="现场图片" class="mb-4" />
      <div>
        <SimpleUpload v-model:id-list="inspectionForm.imageIdList" :max-count="10" :show-upload-list="true" />
      </div>
    </div>
  </BasicPopup>
</template>

<style></style>
