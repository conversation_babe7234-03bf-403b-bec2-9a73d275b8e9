<script setup lang="ts">
import { getInspectionDetailApi, type InspectionInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';

defineEmits(['register']);
const inspectionForm = ref<Partial<InspectionInfo>>({});
const state = reactive<{ id?: number }>({
  id: undefined,
});
const init = (data: { id: number }) => {
  state.id = data.id;
  getDetail();
};
const getDetail = async () => {
  if (state.id) {
    inspectionForm.value = await getInspectionDetailApi(state.id);
  }
};
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="pageTitle" @register="registerPopup" />
</template>

<style></style>
