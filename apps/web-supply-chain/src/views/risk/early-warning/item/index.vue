<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {type EarlyWarningItemInfo, getEarlyWarningItemPageListApi} from '#/api';
import DetailPage from './detail.vue'
import {usePopup} from "@vben/fe-ui";
const dictStore = useDictStore();
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '预警相关企业',
    },
    {
      component: 'Input',
      fieldName: 'ruleName',
      label: '预警规则名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '预警状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'disposalStatus',
      label: '处置状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'alertCode', title: '预警记录' },
    { field: 'projectName', title: '项目名称' },
    { field: 'projectCode', title: '项目编号' },
    { field: 'companyName', title: '预警相关企业' },
    { field: 'ruleName', title: '规则名称' },
    {
      field: 'status',
      title: '预警状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'disposalStatus',
      title: '处置状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    { field: 'createTime', formatter: 'formatDateTime', title: '预警时间', width: 180 },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { categoryCode: string; categoryName: string; status: string },
      ) => {
        return await getEarlyWarningItemPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerPage, { openPopup: openFormPopup }] = usePopup();
const handleItem = (row: EarlyWarningItemInfo) => {
  openFormPopup(true, { data: row, pageType: 'edit' });
};
const viewDetail = (row: EarlyWarningItemInfo) => {
  openFormPopup(true, { data: row, pageType: 'detail' });
};
const editSuccess = () => {
  gridApi.reload();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="handleItem(row)">处置</a-typography-link>
          <a-typography-link @click="viewDetail(row)">详情</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <DetailPage @register="registerPage" @ok="editSuccess" />
  </Page>
</template>

<style></style>
