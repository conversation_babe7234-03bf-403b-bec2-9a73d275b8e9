<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { GridApi, VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
// import { type OutputInvoice, outputInvoiceDetailApi } from '#/api';

const emit = defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();

// 响式状态
const isRecordExpanded = ref(true);

// 切换方法
const toggleRecord = () => {
  isRecordExpanded.value = !isRecordExpanded.value;
};

// 默认数据
const defaultForm: Partial<AddInbound> = {
  id: undefined,
  createBy: 0,
  createTime: '',
  updateBy: 0,
  updateTime: '',
  deleteFlag: 0,
  version: 0,
  inboundReceiptCode: '',
  receiptDate:"",
  projectId: 0,
  projectName: '',
  projectCode: '',
  warehouseId: 0,
  warehouseCode: '',
  warehouseName: '',
  customerCompanyCode: '',
  customerCompanyName: '',
  sourceDocumentType: '',
  deliveryReceiptId: 0,
  deliveryReceiptDisplay: '',
  amountWithTax: 0,
  invoicedAmountWithTax: 0,
  status: '',
  approvalStatus: '',
  invoiceStatus: '',
  remarks: '',
  isSnManaged: 0,
  inboundReceiptSourceRelBOS: [{
    sourceDocumentId: 123,
    sourceDocumentDisplay: '345',
    sourceDocumentType: '555'
  },
  ],
  inboundReceiptItemBOs: [],
};

let detailForm = reactive<Partial<AddInbound>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const title = '销项税票详情';

const init = async (data: any) => {
  if (data.id) {
    const res = await outputInvoiceDetailApi(data.id); // 调用真实 API

    // 深度复制确保响应性
    Object.keys(res).forEach(key => {
      detailForm[key] = res[key];
    });

    // 强制校验并转换inboundReceiptItemBOs字段
    if (!Array.isArray(res.inboundReceiptItemBOs) || res.inboundReceiptItemBOs === null) {
      detailForm.inboundReceiptItemBOs = [];
    } else {
      // 创建全新数组实例确保响应性
      detailForm.inboundReceiptItemBOs = [...res.inboundReceiptItemBOs];
    }

    // 强制刷新表格
    if (gridApiLocation?.grid) {
      gridApiLocation.grid.reloadData(detailForm.inboundReceiptItemBOs);
    }
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
    detailForm.inboundReceiptItemBOs = defaultForm.inboundReceiptItemBOs ? [...defaultForm.inboundReceiptItemBOs] : [];
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();

const labelCol = { style: { width: '150px' } };

// 在表格配置中添加key属性确保强制刷新
const gridLocation: VxeTableGridOptions = {
  data: detailForm.inboundReceiptItemBOs,
  props: {
    key: computed(() => detailForm.id || 'new') // 添加key属性
  },
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      editRender: {},
      slots: { edit: 'edit_product_name' },
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '规格型号',
      editRender: {},
      slots: { edit: 'edit_product_alias' },
      minWidth: '150px',
    },
    {
      field: 'productCode',
      title: '规格属性',
      editRender: {},
      slots: { edit: 'edit_product_code' },
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '计量单位',
      editRender: {},
      slots: { edit: 'edit_specifications' },
      minWidth: '150px',
    },
    { field: 'brandName', title: '商品品牌', editRender: {}, slots: { edit: 'edit_brand_name' }, minWidth: '150px' },
    {
      field: 'originName',
      title: '本次开票数量',
      editRender: {},
      slots: { edit: 'edit_origin_name' },
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '不含税单价',
      editRender: {},
      slots: { edit: 'edit_measure_unit' },
      minWidth: '150px',
    },
    {
      field: 'quantity',
      title: '含税单价',
      editRender: {},
      slots: { edit: 'edit_quantity' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentItemNumber',
      title: '发票不含税金额',
      editRender: {},
      slots: { edit: 'edit_source_document_item_number' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentDisplay',
      title: '发票税额',
      editRender: {},
      slots: { edit: 'edit_source_document_display' },
      minWidth: '150px',
    },
    { field: 'serialNumbers', title: '发票价税合计', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '税率（%）', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '关联销售订单', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '关联销售结算单', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers', title: '销售订单行', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

// 注册表格
const [GridLocation, gridApiLocation] = useVbenVxeGrid({
  gridOptions: gridLocation,
  watch: {
    // 监听detailForm.inboundReceiptItemBOs变化
    data: {
      handler: (newVal) => {
        if (gridApiLocation?.grid && newVal) {
          gridApiLocation.grid.reloadData(newVal);
        }
      },
      deep: true,
      immediate: true
    }
  }
});
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <Form
        ref="formRef"
        :colon="false"
        :model="detailForm"
        :label-col="labelCol"
        :wrapper-col="{ span: 20 }"
        class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">

        <Col v-bind="colSpan">
          <FormItem label="销项税票编号" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="发票属性" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="所属项目名称" name="projectName">
            <Select v-model:value="detailForm.projectName" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="所属项目编号" name="projectCode">
            <Input v-model:value="detailForm.projectCode" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="申请开票日期" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="业务类型" name="deliveryReceiptId">
            <Select v-model:value="detailForm.deliveryReceiptId" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="开票申请编号" name="receiptDate">
            <Input v-model:value="detailForm.receiptDate" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="detailForm.remarks" :rows="3" disabled />
          </FormItem>
        </Col>
      </Row>

      <h3 @click="toggleRecord" class="pl-[50px]">
        记录信息
        <span class="arrow" :class="{ 'arrow-down': !isRecordExpanded, 'arrow-up': isRecordExpanded }">
          ▼
        </span>
      </h3>

      <transition name="slide">
        <Row v-if="isRecordExpanded" class="mt-5">
          <Col v-bind="colSpan">
            <FormItem label="创建时间" name="inboundReceiptCode">
              <Input v-model:value="detailForm.inboundReceiptCode" disabled />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="最后修改时间" name="inboundReceiptCode">
              <Input v-model:value="detailForm.inboundReceiptCode" disabled />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="业务负责人" name="inboundReceiptCode">
              <Input v-model:value="detailForm.inboundReceiptCode" disabled />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="生效时间" name="inboundReceiptCode">
              <Input v-model:value="detailForm.inboundReceiptCode" disabled />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="审批状态" name="inboundReceiptCode">
              <Input v-model:value="detailForm.inboundReceiptCode" disabled />
            </FormItem>
          </Col>
          <Col v-bind="colSpan">
            <FormItem label="业务状态" name="inboundReceiptCode">
              <Input v-model:value="detailForm.inboundReceiptCode" disabled />
            </FormItem>
          </Col>
        </Row>
      </transition>

      <!-- 税票信息 -->
      <BasicCaption content="税票信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="税票类型" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="发票代码" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="发票号码" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="开票日期" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="销售方" name="projectName">
            <Select v-model:value="detailForm.projectName" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="购买方" name="projectName">
            <Select v-model:value="detailForm.projectName" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="销售方纳税人识别号" name="projectCode">
            <Input v-model:value="detailForm.projectCode" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="购买方纳税人识别号" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="销售方地址、电话" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="购买方地址、电话" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="销售方开户银行" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="购买方开户银行" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="销售方银行账号" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="购买方银行账号" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>
      </Row>

      <!-- 发票明细 -->
      <BasicCaption content="发票明细" />
      <div class="invoice-details">
        <!--        <Row>-->
        <!--          <Col v-bind="colSpan">-->
        <!--            <FormItem label="关联销售结算单" name="sourceDocumentType">-->
        <!--              <Select v-model:value="detailForm.sourceDocumentType" disabled />-->
        <!--            </FormItem>-->
        <!--          </Col>-->
        <!--        </Row>-->
        <GridLocation>
          <template #edit_product_name="{ row }">
            <Input v-model:value="row.productName" disabled />
          </template>

          <template #edit_product_alias="{ row }">
            <Input v-model:value="row.productAlias" disabled />
          </template>

          <template #edit_product_code="{ row }">
            <Input v-model:value="row.productCode" disabled />
          </template>

          <template #edit_specifications="{ row }">
            <Input v-model:value="row.specifications" disabled />
          </template>

          <template #edit_brand_name="{ row }">
            <Input v-model:value="row.brandName" disabled />
          </template>

          <template #edit_origin_name="{ row }">
            <Input v-model:value="row.originName" disabled />
          </template>

          <template #edit_measure_unit="{ row }">
            <Input v-model:value="row.measureUnit" disabled />
          </template>

          <template #edit_quantity="{ row }">
            <Input v-model:value="row.quantity" disabled />
          </template>

          <template #edit_source_document_item_number="{ row }">
            <Input v-model:value="row.sourceDocumentItemNumber" disabled />
          </template>

          <template #edit_source_document_display="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" disabled />
          </template>

          <template #edit_serial_numbers="{ row }">
            <Input v-model:value="row.serialNumbers" disabled />
          </template>

          <template #edit_remarks="{ row }">
            <Input v-model:value="row.remarks" disabled />
          </template>
        </GridLocation>
        <!--        <Row>-->
        <!--          <Col v-bind="colSpan">-->
        <!--            <FormItem label="移交人" name="sourceDocumentType">-->
        <!--              <Select v-model:value="detailForm.sourceDocumentType" disabled />-->
        <!--            </FormItem>-->
        <!--          </Col>-->
        <!--        </Row>-->
      </div>

      <!-- 进项票记录 -->
      <BasicCaption content="进项票记录" />
      <div>
        <GridLocation>
          <template #edit_product_name="{ row }">
            <Input v-model:value="row.productName" disabled />
          </template>

          <template #edit_product_alias="{ row }">
            <Input v-model:value="row.productAlias" disabled />
          </template>

          <template #edit_product_code="{ row }">
            <Input v-model:value="row.productCode" disabled />
          </template>

          <template #edit_specifications="{ row }">
            <Input v-model:value="row.specifications" disabled />
          </template>

          <template #edit_brand_name="{ row }">
            <Input v-model:value="row.brandName" disabled />
          </template>

          <template #edit_origin_name="{ row }">
            <Input v-model:value="row.originName" disabled />
          </template>

          <template #edit_measure_unit="{ row }">
            <Input v-model:value="row.measureUnit" disabled />
          </template>

          <template #edit_quantity="{ row }">
            <Input v-model:value="row.quantity" disabled />
          </template>

          <template #edit_source_document_item_number="{ row }">
            <Input v-model:value="row.sourceDocumentItemNumber" disabled />
          </template>
        </GridLocation>
        <!--        <Row>-->
        <!--          <Col v-bind="colSpan">-->
        <!--            <FormItem label="收款人" name="sourceDocumentType">-->
        <!--              <Select v-model:value="detailForm.sourceDocumentType" disabled />-->
        <!--            </FormItem>-->
        <!--          </Col>-->
        <!--          <Col v-bind="colSpan">-->
        <!--            <FormItem label="开票人" name="sourceDocumentType">-->
        <!--              <Select v-model:value="detailForm.sourceDocumentType" disabled />-->
        <!--            </FormItem>-->
        <!--          </Col>-->
        <!--          <Col v-bind="colSpan">-->
        <!--            <FormItem label="复核人" name="sourceDocumentType">-->
        <!--              <Select v-model:value="detailForm.sourceDocumentType" disabled />-->
        <!--            </FormItem>-->
        <!--          </Col>-->
        <!--        </Row>-->
      </div>
    </Form>
  </BasicPopup>
</template>

<style scoped>
:where(.css-dev-only-do-not-override-1gaak89).ant-picker {
  width: 100%;
}

.arrow {
  transition: transform 0.3s ease;
  display: inline-block;
  margin-left: 8px;
}
.arrow-down {
  transform: rotate(0deg);
}
.arrow-up {
  transform: rotate(180deg);
}
.slide-enter-active, .slide-leave-active {
  transition: max-height 0.5s ease-out;
  max-height: 200px;
}
.slide-enter-from, .slide-leave-to {
  max-height: 0;
  overflow: hidden;
}

.invoice-details :where(.css-dev-only-do-not-override-mroem5).ant-form-item{
  margin-bottom: 0;
  margin-top: 24px;
}

</style>
