<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OrderInfo } from '#/api';

import { ref, watch } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { infoSalesReturnListApi } from '#/api';
import { formatFun, plusFun } from '#/utils/calculate';

defineEmits(['register']);
const orderDetail = ref<OrderInfo>({});

const init = async (data: OrderInfo) => {
  if (data?.id) {
    orderDetail.value = await infoSalesReturnListApi({ id: data.id });
  }
};
const [registerPopup] = usePopupInner(init);

const baseGridOptions = {
  showFooter: true,
  pagerConfig: {
    enabled: false,
  },
  border: 'inner',
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const bankGridOptions = {
  columns: [
    { field: 'checkbox', width: '50px', fixed: 'left' },
    { field: 'productName', title: '商品名称', minWidth: '160px' },
    { field: 'productAlias', title: '商品别名', minWidth: '160px' },
    { field: 'specifications', title: '规格型号', minWidth: '160px' },
    { field: 'productCode', title: '商品编码', minWidth: '160px' },
    { field: 'measureUnit', title: '计量单位', minWidth: '160px' },
    { field: 'brandName', title: '商品品牌', minWidth: '160px' },
    { field: 'originName', title: '生产厂家', minWidth: '160px' },
    { field: 'returnQuantity', title: '退货重量', minWidth: '160px' },
    { field: 'priceWithTax', title: '含税单价', minWidth: '160px' },
    { field: 'taxRate', title: '税率(%)', minWidth: '160px' },
    { field: 'amountWithTax', title: '含税金额', minWidth: '160px' },
    { field: 'taxAmount', title: '税额', minWidth: '160px' },
    { field: 'amountWithoutTax', title: '不含税金额', minWidth: '160px' },
    { field: 'priceWithoutTax', title: '不含税单价', minWidth: '160px' },
    { field: 'itemNumber', title: '采购订单行号', minWidth: '160px' },
    { field: 'remarks', title: '备注', minWidth: '160px' },
  ],
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let amountWithTax = 0;
    let taxAmount = 0;
    let amountWithoutTax = 0;
    data.forEach((item) => {
      amountWithTax = plusFun(amountWithTax, item.amountWithTax || 0);
      taxAmount = plusFun(taxAmount, item.taxAmount || 0);
      amountWithoutTax = plusFun(amountWithoutTax, item.amountWithoutTax || 0);
    });
    const footerRow = {
      checkbox: '合计',
      amountWithTax: formatFun(amountWithTax),
      taxAmount: formatFun(taxAmount),
      amountWithoutTax: formatFun(amountWithoutTax),
    };
    return [footerRow];
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [ProducGrid, productGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
watch(
  () => orderDetail.value,
  (val = {}) => {
    productGridApi.grid.reloadData(val.salesReturnOrderItemVOS ?? []);
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="订单信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="销售退货订单编号">
          {{ orderDetail.salesReturnOrderCode }}
        </a-descriptions-item>
        <a-descriptions-item label="销售退货订单名称">
          {{ orderDetail.salesReturnOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="所属项目名称">
          {{ orderDetail.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="所属项目编号">
          {{ orderDetail.projectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="关联销售订单">
          {{ orderDetail.salesOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="贸易执行企业">
          {{ orderDetail.executorCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="上游企业">
          {{ orderDetail.supplierName }}
        </a-descriptions-item>
        <a-descriptions-item label="任务类型">
          {{ orderDetail.supplierName }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">
          {{ orderDetail.createTime }}
        </a-descriptions-item>
        <a-descriptions-item label="最后修改时间">
          {{ orderDetail.updateTime }}
        </a-descriptions-item>
        <a-descriptions-item label="业务负责人">
          {{ orderDetail.businessManagerName }}
        </a-descriptions-item>
        <a-descriptions-item label="生效时间">
          {{ orderDetail.updateTime }}
        </a-descriptions-item>
        <a-descriptions-item label="审批状态">
          {{ orderDetail.approvalStatus }}
        </a-descriptions-item>
        <a-descriptions-item label="业务状态">
          {{ orderDetail.state }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="业务信息" />
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="业务日期">
          {{ orderDetail.businessDate }}
        </a-descriptions-item>
        <a-descriptions-item label="预计结束日期">
          {{ orderDetail.estimatedEndDate }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="商品信息" />
      <ProducGrid />
    </div>
  </BasicPopup>
</template>

<style></style>
