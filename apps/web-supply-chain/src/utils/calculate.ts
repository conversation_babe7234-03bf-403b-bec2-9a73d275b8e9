import BigNumber from 'bignumber.js';

// 加法
export const plusFun = (a: number | string, b: number | string): number => {
  return new BigNumber(a).plus(b).toNumber();
};
// 减法
export const minusFun = (a: number | string, b: number | string): number => {
  return new BigNumber(a).minus(b).toNumber();
};
// 乘法
export const timesFun = (a: number | string, b: number | string): number => {
  return new BigNumber(a).times(b).toNumber();
};
// 除法
export const dividedByFun = (a: number | string, b: number | string): number => {
  return new BigNumber(a).dividedBy(b).toNumber();
};
// 格式化
export const formatFun = (value: number | string, decimalPlaces = 2): string => {
  return new BigNumber(value).toFixed(decimalPlaces);
};
/**
 * 计算税额：税额 = 含税金额 - (含税金额 / (1 + 税率))
 *          税额 = 含税金额 * (税率 / (1 + 税率))
 * @param amountWithTax 含税金额
 * @param taxRate 税率
 */
export const calculateTaxAmount = (amountWithTax: number | string, taxRate: number | string): number => {
  const amount = new BigNumber(amountWithTax);
  const rate = new BigNumber(taxRate);
  const taxAmount = amount.times(rate).dividedBy(new BigNumber(1).plus(rate));
  return taxAmount.toNumber();
};
