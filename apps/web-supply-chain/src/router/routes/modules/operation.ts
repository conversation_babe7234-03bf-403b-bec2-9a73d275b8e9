import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: '',
      order: -1,
      title: $t('page.operation.title'),
    },
    name: 'Operation',
    path: '/operation',
    children: [
      {
        meta: {
          icon: '',
          order: -1,
          title: $t('page.operation.inspection.title'),
        },
        name: 'OperationInspection',
        path: '/operation/inspection',
        children: [
          {
            name: 'OperationInspectionManage',
            path: '/operation/inspection/manage',
            component: () => import('#/views/operation/inspection/manage/index.vue'),
            meta: {
              icon: '',
              title: $t('page.operation.inspection.manage'),
            },
          },
        ],
      },
    ],
  },
];

export default routes;
