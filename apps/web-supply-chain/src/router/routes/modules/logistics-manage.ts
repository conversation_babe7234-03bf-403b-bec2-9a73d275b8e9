import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '物流管理',
    },
    name: 'LogisticsManage',
    path: '/logistics-manage',
    children: [
      {
        name: 'ShippingDocument',
        path: 'shipping-document',
        component: () => import('#/views/logistics-manage/shipping-document/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '发货单据管理',
        },
      },
      {
        name: 'TransOrder',
        path: 'trans-order',
        // component: () => import('#/views/logistics-manage/trans-order/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '运输指令单',
        },
      },
    ],
  },
];

export default routes;
