import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '税票登记',
    },
    name: 'InvoiceRegister',
    path: '/invoice-register',
    children: [
      {
        name: 'InvoiceRequest',
        path: 'invoice-request',
        component: () => import('#/views/invoice-register/invoice-request/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '开票申请',
        },
      },
      {
        name: 'OutputInvoice',
        path: 'output-invoice',
        component: () => import('#/views/invoice-register/output-invoice/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '销项税票记录',
        },
      },
      {
        name: 'InputInvoice',
        path: 'input-invoice',
        component: () => import('#/views/invoice-register/input-invoice/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '进项税票记录',
        },
      },
    ],
  },
];

export default routes;
