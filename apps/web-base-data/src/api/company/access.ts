import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';
/**
 * 企业准入
 */
export interface AccessInfo {
  // 准入角色
  accessRole?: string;
  // 企业Id
  companyId?: number;
  // 主键
  id?: number;
  // 备注
  remark?: string;
  [property: string]: any;
}

export async function getAccessPageListApi(params: PageListParams) {
  return requestClient.get('/base/company/access/page', { params });
}
export async function getAccessDetailApi(params: { id: number }) {
  return requestClient.get('/base/company/access/detail', { params });
}
export async function addAccessApi(data: AccessInfo) {
  return requestClient.post('/base/company/access/add', data);
}
export async function editAccessApi(data: AccessInfo) {
  return requestClient.post('/base/company/access/edit', data);
}
export async function deleteAccessApi(id: number) {
  return requestClient.post('/base/company/access/delete', {}, { params: { id } });
}
export async function cancelAccessApi(id: number) {
  return requestClient.post('/base/company/access/cancel', {}, { params: { id } });
}
export async function auditAccessApi(data: { id: number; isPass: number; remark: string }) {
  return requestClient.post('/base/company/access/review', data);
}
