<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ContractInfo } from '#/api';

import { ref } from 'vue';

import { BaseUpload, OnlyOffice } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { getCombinedErrorMessagesString } from '@vben/utils';

import { AutoComplete, message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { getContractSignInfoApi, importMaterialApi, saveContractSignApi } from '#/api';

const emit = defineEmits(['ok', 'register']);
const dictStore = useDictStore();
const init = async (data: ContractInfo) => {
  contractForm.value = data.id
    ? await getContractSignInfoApi({ id: data.id })
    : {
        ...data,
      };
  await gridApi.grid.reloadData(contractForm.value.signDetailList ?? []);
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const modalTitle = ref('新增合同');
const ContractFormRef = ref();
const contractForm = ref<ContractInfo>({});
const rules = {
  contractName: [{ required: true, message: '请输入合同名称' }],
  contractCode: [{ required: true, message: '请输入合同编码' }],
  signMethod: [{ required: true, message: '请选择签约方式', trigger: 'change' }],
};
const colSpanProp = COL_SPAN_PROP;
const save = async () => {
  await ContractFormRef.value.validate();
  const errMap = await gridApi.grid.validate(true);
  if (errMap) {
    const errMessage = getCombinedErrorMessagesString(errMap);
    if (errMessage) {
      message.error(errMessage);
      return;
    }
  }
  changeOkLoading(true);
  try {
    const formData = cloneDeep(contractForm.value);
    const { visibleData } = gridApi.grid.getTableData();
    formData.signDetailList = visibleData;
    const res = await saveContractSignApi(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const baseGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const signGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'signerType',
      title: '类型',
      minWidth: '100px',
    },
    {
      field: 'signatoryParamName',
      title: '签约角色',
      slots: { default: 'edit_signatory_param_name' },
      minWidth: '160px',
    },
    {
      field: 'signInfo',
      title: '签约方信息',
      slots: { default: 'edit_sign_info' },
      minWidth: '500px',
    },
  ],
  editRules: {
    signatoryParamName: [{ required: true, content: '请输入签约角色' }],
    signInfo: [
      {
        validator({ row }) {
          if (row.signerType === '1' && !row.signatoryOrg) {
            return new Error('请输入签约企业');
          }
          if (row.signerType === '0' && !row.signatoryName) {
            return new Error('请输入真实姓名');
          }
          // if (!row.signatoryAccount) {
          //   return new Error('请输入手机号或邮箱');
          // }
          return true;
        },
      },
    ],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: signGridOptions,
});
const addSignatory = (type: string) => {
  gridApi.grid.insertAt({
    signerType: type,
  });
};
const removeSignatory = () => {
  const records = gridApi.grid.getCheckboxRecords();
  gridApi.grid.remove(records);
};
</script>

<template>
  <BasicPopup :title="modalTitle" show-ok-btn @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="ContractFormRef" class="" :model="contractForm" :rules="rules" v-bind="FORM_PROP">
        <a-row class="mt-5">
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同名称" name="contractName">
              <a-input v-model:value="contractForm.contractName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同编码" name="contractCode">
              <a-input v-model:value="contractForm.contractCode" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="签约类型" name="signMethod">
              <a-select v-model:value="contractForm.signMethod" :options="dictStore.getDictList('baseEnableType')" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="设置签约方" />
        <Grid>
          <template #toolbarTools>
            <a-space>
              <a-button type="primary" @click="addSignatory('0')">添加签约个人</a-button>
              <a-button type="primary" @click="addSignatory('1')">添加签约企业</a-button>
              <a-button type="primary" danger @click="removeSignatory">删除</a-button>
            </a-space>
          </template>
          <template #edit_signatory_param_name="{ row }">
            <AutoComplete v-model:value="row.signatoryParamName" class="w-full" />
          </template>
          <template #edit_sign_info="{ row }">
            <a-space>
              <a-input v-if="row.signerType === '1'" v-model:value="row.signatoryOrg" placeholder="请输入签约企业" />
              <a-input v-model:value="row.signatoryName" placeholder="请输入真实姓名" />
              <a-input v-model:value="row.signatoryAccount" placeholder="请输入手机号或邮箱" />
            </a-space>
          </template>
          <template #sign_info="{ row }">
            <a-space>
              <span v-if="row.signerType === '1'">{{ row.signatoryOrg }}</span>
              <span>{{ row.signatoryName }}</span>
              <span>{{ row.signatoryAccount }}</span>
            </a-space>
          </template>
        </Grid>
        <BasicCaption content="合同文件">
          <template v-if="contractForm.createType === '2'" #action>
            <a-button type="primary">保存</a-button>
          </template>
        </BasicCaption>
        <a-row v-if="contractForm.createType === '1'" class="mt-5">
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同（源文件）" name="contractFile">
              <BaseUpload :upload-api="importMaterialApi" />
            </a-form-item>
          </a-col>
        </a-row>
        <template v-else-if="contractForm.createType === '2'">
          <OnlyOffice :get-config-api="getContractSignInfoApi" />
        </template>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
