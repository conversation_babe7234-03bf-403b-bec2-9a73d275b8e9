<script lang="ts" setup>
import { defineEmits, ref } from 'vue';

import { OnlyOffice } from '@vben/base-ui';
import { useVbenModal } from '@vben/common-ui';

import { Empty, message } from 'ant-design-vue';
import { find } from 'lodash-es';

const props = defineProps({
  getTemplateListApi: {
    type: Function,
    required: true,
  },
  getConfigApi: {
    type: Function,
    required: true,
  },
});
const emit = defineEmits<Emits>();

// createType 1 直接创建 2 模板创建
export interface CreateContractData {
  createType: '1' | '2';
  templateId?: number;
}

// 定义 Emits 接口
interface Emits {
  (event: 'confirmPick', payload: CreateContractData): void;
}

// 定义表单数据类型
interface ContractForm {
  templateId?: number;
}
const [Modal, modalApi] = useVbenModal({
  confirmText: '使用模板生成合同',
  onConfirm: () => {
    confirm();
  },
  onCancel: () => {
    close();
  },
});

const contractForm = ref<ContractForm>({});
const rules = {};
const init = async () => {
  modalApi.open();
  templateList.value = await props.getTemplateListApi();
};

// 定义模板列表项类型
interface TemplateItem {
  pid: string;
  templateName: string;
  fileId: string;
}

const templateList = ref<TemplateItem[]>([]);

const confirm = () => {
  if (!contractForm.value.templateId) {
    return message.error('请选择模板');
  }
  const formData: CreateContractData = { createType: '2', templateId: contractForm.value.templateId };
  emit('confirmPick', formData);
  modalApi.close();
};

const close = () => {
  contractForm.value = {};
};

const editorRef = ref<InstanceType<typeof OnlyOffice> | null>(null);

const changeTemplateId = (pid: string) => {
  // 关键修改：明确 find 的结果类型
  const templateInfo: TemplateItem | undefined = find(templateList.value, { pid });

  // 检查 templateInfo 是否存在并且包含 fileId 属性
  if (editorRef.value && templateInfo && templateInfo.fileId) {
    editorRef.value.init(templateInfo.fileId, 'view');
  }
};

const createByText = () => {
  const formData: CreateContractData = { createType: '1' };
  emit('confirmPick', formData);
  modalApi.close();
};

defineExpose({
  init,
  modalApi,
});
</script>

<template>
  <Modal class="w-[1200px]">
    <div class="form-area">
      <div class="form-inner">
        <a-form :model="contractForm" :rules="rules" label-suffix="：">
          <a-form-item name="type" label="模板名称">
            <a-select
              v-model:value="contractForm.templateId"
              :options="templateList"
              :field-names="{ label: 'templateName', value: 'id' }"
              @change="changeTemplateId"
            />
          </a-form-item>
        </a-form>
      </div>
      <div class="ml-4 mr-4 h-[500px]">
        <Empty v-if="!contractForm.templateId" description="请选择模板" />
        <OnlyOffice v-else ref="editorRef" :get-config-api="getConfigApi" />
      </div>
    </div>
    <template #center-footer>
      <a-button type="primary" @click="createByText"> 跳过选择，自行上传合同文件 </a-button>
    </template>
  </Modal>
</template>

<style lang="scss"></style>
