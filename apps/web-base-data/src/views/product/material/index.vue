<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MaterialInfo } from '#/api';

import { ImportData } from '@vben/base-ui';
import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteMaterialApi,
  disableMaterialApi,
  downloadMaterialTemplateApi,
  enableMaterialApi,
  exportMaterialApi,
  getMaterialPageListApi,
  getTaxonomyTreeListApi,
  importMaterialApi,
} from '#/api';

import MaterialForm from './components/material-form.vue';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'ApiTreeSelect',
      fieldName: 'categoryId',
      label: '商品分类',
      componentProps: {
        api: getTaxonomyTreeListApi,
        labelField: 'categoryName',
        valueField: 'id',
        childrenField: 'children',
      },
    },
    {
      component: 'Input',
      fieldName: 'productCode',
      label: '商品编码',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '商品名称',
    },
    {
      component: 'Input',
      fieldName: 'productAlias',
      label: '商品别名',
    },
    {
      component: 'Input',
      fieldName: 'specification',
      label: '规格型号',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '商品状态',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'categoryName', title: '商品分类', minWidth: 200 },
    { field: 'productCode', title: '商品编码' },
    { field: 'productName', title: '商品名称', minWidth: 200 },
    { field: 'productAlias', title: '商品别名' },
    { field: 'specifications', title: '规格型号' },
    { field: 'measureUnit', title: '计量单位' },
    { field: 'brandName', title: '牌号/材质' },
    { field: 'originName', title: '产地/厂商' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues: { spuCode: string; spuName: string; status: string }) => {
        return await getMaterialPageListApi({
          ...formValues,
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  // 连接抽离的组件
  connectedComponent: MaterialForm,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      gridApi.reload();
    }
  },
});
const add = () => {
  modalApi.setData({}).open();
};
const edit = (row: MaterialInfo) => {
  modalApi.setData(row).open();
};
const changeMaterialStatus = (status: 0 | 1) => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.id);
  const operation = {
    1: {
      label: '启用',
      api: enableMaterialApi,
    },
    0: {
      label: '禁用',
      api: disableMaterialApi,
    },
  };
  AntdModal.confirm({
    title: `确认${operation[status].label}`,
    content: `确认${operation[status].label}此商品吗？`,
    async onOk() {
      await operation[status].api(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const delMaterial = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.id);
  AntdModal.confirm({
    title: '确认删除',
    content: '确认删除此商品吗？',
    async onOk() {
      await deleteMaterialApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="add()">新增商品</a-button>
          <ImportData
            :upload-api="importMaterialApi"
            :download-template-api="downloadMaterialTemplateApi"
            @import-success="gridApi.reload"
          />
          <a-button type="primary" @click="exportMaterialApi">导出</a-button>
          <a-button type="primary" @click="changeMaterialStatus(1)">启用</a-button>
          <a-button type="primary" danger @click="changeMaterialStatus(0)">禁用</a-button>
          <a-button type="primary" danger @click="delMaterial()">删除</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-typography-link @click="edit(row)">编辑</a-typography-link>
      </template>
    </Grid>
    <Modal />
  </Page>
</template>

<style></style>
