<script setup lang="ts">
import type { MaterialInfo } from '#/api/product/material';

import { ref, unref } from 'vue';

import { ApiComponent, useVbenModal } from '@vben/common-ui';

import { message, Select, TreeSelect } from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { getAttributeUnitList, getTaxonomyTreeListApi } from '#/api';
import { addMaterialApi, editMaterialApi } from '#/api/product/material';

const materialFormRef = ref();
const materialForm = ref<MaterialInfo>({});

const modalTitle = ref('新增商品');
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {
    const data = modalApi.getData() ?? {};
    modalTitle.value = data.id ? '编辑商品' : '新增商品';
    materialForm.value = defaultsDeep(data, {});
  },
  onConfirm: async () => {
    await materialFormRef.value.validate();
    let api = addMaterialApi;
    if (materialForm.value.id) {
      api = editMaterialApi;
    }
    try {
      modalApi.lock();
      await api(unref(materialForm));
      message.success('保存成功');
      await modalApi.close();
    } finally {
      modalApi.unlock();
    }
  },
  onBeforeClose: () => {
    materialForm.value = {};
    materialFormRef.value.resetFields();
    return true;
  },
});

const materialRules = {
  productName: [{ required: true, message: '请输入商品名称' }],
  productCode: [{ required: true, message: '请输入商品编码' }],
  categoryId: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  measureUnit: [{ required: true, message: '请选择计量单位', trigger: 'change' }],
};
</script>

<template>
  <Modal :title="modalTitle">
    <a-form
      ref="materialFormRef"
      :model="materialForm"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      :colon="false"
      :rules="materialRules"
    >
      <a-form-item label="商品分类" name="categoryId">
        <ApiComponent
          v-model="materialForm.categoryId as unknown as string"
          :component="TreeSelect"
          :api="getTaxonomyTreeListApi"
          label-field="categoryName"
          value-field="id"
          children-field="children"
          model-prop-name="value"
          options-prop-name="treeData"
        />
      </a-form-item>
      <a-form-item label="商品编码" name="productCode">
        <a-input v-model:value="materialForm.productCode" />
      </a-form-item>
      <a-form-item label="商品名称" name="productName">
        <a-input v-model:value="materialForm.productName" />
      </a-form-item>
      <a-form-item label="商品别名" name="productAlias">
        <a-input v-model:value="materialForm.productAlias" />
      </a-form-item>
      <a-form-item label="规格型号" name="specifications">
        <a-input v-model:value="materialForm.specifications" />
      </a-form-item>
      <a-form-item label="计量单位" name="measureUnit">
        <ApiComponent
          v-model="materialForm.measureUnit"
          :component="Select"
          :api="getAttributeUnitList"
          label-field="unitName"
          value-field="unitName"
          model-prop-name="value"
        />
      </a-form-item>
      <a-form-item label="牌号/材质" name="brandName">
        <a-input v-model:value="materialForm.brandName" />
      </a-form-item>
      <a-form-item label="产地/厂商" name="originName">
        <a-input v-model:value="materialForm.originName" />
      </a-form-item>
      <!--<a-form-item label="备注" name="remarks">-->
      <!--  <a-textarea v-model:value="materialForm.remarks" :rows="4" />-->
      <!--</a-form-item>-->
    </a-form>
  </Modal>
</template>

<style></style>
