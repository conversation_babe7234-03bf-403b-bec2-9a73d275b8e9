<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getSpuPageListApi } from '#/api';
import GoodsEdit from '#/views/product/manage/components/goods-edit.vue';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'categoryCode',
      label: '商品编码',
    },
    {
      component: 'Input',
      fieldName: 'categoryName',
      label: '商品名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '商品状态',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'spuCode', title: '商品编码', minWidth: 200 },
    { field: 'spuName', title: '商品名称' },
    { field: 'categoryName', title: '商品分类' },
    { field: 'specification', title: '规格型号' },
    { field: 'measureUnit', title: '计量单位' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues: { spuCode: string; spuName: string; status: string }) => {
        return await getSpuPageListApi({
          ...formValues,
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="openFormPopup(true, {})">新增</a-button>
      </template>
      <template #action="{ row }">
        <a-typography-link @click="openFormPopup(true, row)">编辑</a-typography-link>
      </template>
    </Grid>
    <GoodsEdit @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
