<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { BusinessRoleInfo } from '#/api';

import { reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { FORM_PROP } from '@vben/constants';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addBusinessRoleApi, deleteBusinessRoleApi, editBusinessRoleApi, getBusinessRolePageListApi } from '#/api';

const dictStore = useDictStore();
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'roleCode',
      label: '业务角色编码',
    },
    {
      component: 'Input',
      fieldName: 'roleName',
      label: '业务角色名称',
    },
  ],
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'roleCode', title: '业务角色编码' },
    { field: 'roleName', title: '业务角色名称', minWidth: 120 },
    { field: 'isAccess', title: '是否准入', formatter: ['formatBoolean'] },
    { field: 'isReview', title: '是否审批', formatter: ['formatBoolean'] },
    { field: 'reviewCode', title: '审批编码' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues: { spuCode: string; spuName: string; status: string }) => {
        return await getBusinessRolePageListApi({
          ...formValues,
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: BusinessRoleInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const BusinessRoleFormRef = ref();
const defaultBusinessRole = {
  isAccess: 0,
  isReview: 0,
};
const businessRoleForm = ref<BusinessRoleInfo>({ ...defaultBusinessRole });
const rules = reactive({
  roleCode: [{ required: true, message: '请输入业务角色编码', trigger: 'change' }],
  roleName: [{ required: true, message: '请输入业务角色名称', trigger: 'change' }],
  isAccess: [{ required: true, message: '请选择是否准入', trigger: 'change' }],
  isReview: [{ required: true, message: '请选择是否审批', trigger: 'change' }],
  reviewCode: [{ required: false, message: '请输入审批编码', trigger: 'change' }],
});
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await BusinessRoleFormRef.value.validate();
    modalApi.lock(true);
    let api = addBusinessRoleApi;
    if (businessRoleForm.value.id) {
      api = editBusinessRoleApi;
    }
    try {
      await api(businessRoleForm.value);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } finally {
      modalApi.unlock();
    }
  },
  onClosed: () => {
    businessRoleForm.value = cloneDeep(defaultBusinessRole);
    BusinessRoleFormRef.value.resetFields();
    if (rules.reviewCode && rules.reviewCode[0]) {
      rules.reviewCode[0].required = false;
    }
  },
});
const modalTitle = ref('新增业务角色');
const add = () => {
  modalTitle.value = '新增业务角色';
  modalApi.open();
};
const edit = (row: BusinessRoleInfo) => {
  modalTitle.value = '编辑业务角色';
  businessRoleForm.value = cloneDeep(row);
  if (businessRoleForm.value.isAccess && rules.reviewCode && rules.reviewCode[0]) {
    rules.reviewCode[0].required = businessRoleForm.value.isReview !== 0;
  }
  modalApi.open();
};
const delMaterial = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '确认删除此业务角色吗？',
    async onOk() {
      await deleteBusinessRoleApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const changeNeedAudit = (value: number) => {
  if (rules.reviewCode && rules.reviewCode[0]) {
    rules.reviewCode[0].required = value !== 0;
  }
  BusinessRoleFormRef.value.validateFields('reviewCode');
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="add()">新增</a-button>
          <a-button type="primary" danger @click="delMaterial()">删除</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="edit(row)">编辑</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <Modal :title="modalTitle">
      <a-form
        ref="BusinessRoleFormRef"
        :model="businessRoleForm"
        :rules="rules"
        v-bind="{ ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } }"
      >
        <a-form-item label="业务角色编码" name="roleCode">
          <a-input v-model:value="businessRoleForm.roleCode" />
        </a-form-item>
        <a-form-item label="业务角色名称" name="roleName">
          <a-input v-model:value="businessRoleForm.roleName" />
        </a-form-item>
        <a-form-item label="是否准入" name="isAccess">
          <a-select v-model:value="businessRoleForm.isAccess" :options="dictStore.getDictList('baseBooleanType')" />
        </a-form-item>
        <template v-if="businessRoleForm.isAccess">
          <a-form-item label="是否审批" name="isReview">
            <a-select
              v-model:value="businessRoleForm.isReview"
              :options="dictStore.getDictList('baseBooleanType')"
              @change="changeNeedAudit"
            />
          </a-form-item>
          <a-form-item label="审批编码" name="reviewCode">
            <a-input v-model:value="businessRoleForm.reviewCode" />
          </a-form-item>
        </template>
      </a-form>
    </Modal>
  </Page>
</template>

<style></style>
