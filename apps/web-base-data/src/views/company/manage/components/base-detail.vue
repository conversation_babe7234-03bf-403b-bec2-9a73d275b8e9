<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { watch } from 'vue';

import { DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import CompanyBaseDetail from '#/views/company/components/company-base-detail.vue';
import CompanyLegalPersonDetail from '#/views/company/components/company-legal-person-detail.vue';

const props = defineProps({ companyInfo: { type: Object, default: () => ({}) } });
const baseGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  border: 'inner',
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const bankGridOptions = {
  columns: [
    { field: 'accountName', title: '账户名称', minWidth: '160px' },
    { field: 'account', title: '银行账号', minWidth: '160px' },
    { field: 'bank', title: '开户银行', minWidth: '160px' },
    { field: 'branchNumber', title: '开户行号', minWidth: '160px' },
    { field: 'isDefault', title: '是否默认', minWidth: '160px' },
  ],
  ...baseGridOptions,
} as VxeTableGridOptions;
const [BankGrid, bankGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
const invoiceGridOptions = {
  columns: [
    { field: 'title', title: '抬头名称', minWidth: '160px' },
    { field: 'taxNumber', title: '纳税人识别号', minWidth: '160px' },
    { field: 'bank', title: '开户银行', minWidth: '160px' },
    { field: 'account', title: '开户银行账号', minWidth: '160px' },
    { field: 'phone', title: '电话', minWidth: '160px' },
    { field: 'address', title: '地址', minWidth: '160px' },
    { field: 'isDefault', title: '是否默认', minWidth: '160px', fixed: 'right' },
  ],
  ...baseGridOptions,
} as VxeTableGridOptions;
const [InvoiceGrid, invoiceGridApi] = useVbenVxeGrid({
  gridOptions: invoiceGridOptions,
});
watch(
  () => props.companyInfo,
  (val = {}) => {
    bankGridApi.grid.reloadData(val.companyBankList ?? []);
    invoiceGridApi.grid.reloadData(val.companyInvoiceList ?? []);
  },
  { deep: true },
);
</script>

<template>
  <div>
    <!-- 基本信息 -->
    <CompanyBaseDetail :company-info="companyInfo" />
    <!-- 法人基本信息 -->
    <CompanyLegalPersonDetail :company-info="companyInfo" />
    <BasicCaption content="企业联系人信息" />
    <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
      <a-descriptions-item label="联系人姓名">
        {{ companyInfo.contactPersonName }}
      </a-descriptions-item>
      <a-descriptions-item label="联系人电话">
        {{ companyInfo.contactPersonPhone }}
      </a-descriptions-item>
    </a-descriptions>
    <BasicCaption content="客户负责人" />
    <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
      <a-descriptions-item label="业务经理">
        {{ companyInfo.manager }}
      </a-descriptions-item>
    </a-descriptions>
    <BasicCaption content="银行账户" />
    <BankGrid />
    <BasicCaption content="开票信息" />
    <InvoiceGrid />
    <BasicCaption content="业务角色" />
    <a-space class="my-4">
      <a-tag
        v-for="item in companyInfo.roleList"
        :key="item.id"
        :color="item.status === 'EFFECTIVE' ? 'blue' : ''"
        class="text-sm"
      >
        {{ item.name }}
      </a-tag>
    </a-space>
  </div>
</template>

<style></style>
