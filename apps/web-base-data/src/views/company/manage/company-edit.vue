<script setup lang="ts">
import type { CompanyInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { addCompanyApi, editCompanyApi, getCompanyInfoApi } from '#/api';
import BaseForm from '#/views/company/manage/components/base-form.vue';
import {BASE_PAGE_CLASS_NAME} from "@vben/constants";

const emit = defineEmits(['ok', 'register']);
const companyForm = ref<CompanyInfo>({
  companyCode: '',
  companyName: '',
});
const state = reactive({
  activeKey: 'base',
});
const init = async (data: CompanyInfo) => {
  if (data?.id) {
    companyForm.value = await getCompanyInfoApi({ id: data.id as number });
    BaseFormRef.value.init(companyForm.value);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const BaseFormRef = ref();
const save = async () => {
  await BaseFormRef.value.save();
  const formData = cloneDeep(companyForm.value);
  changeOkLoading(true);
  let api = addCompanyApi;
  if (formData.id) {
    api = editCompanyApi;
  }
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="企业信息" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-tabs v-model:active-key="state.activeKey">
        <a-tab-pane key="base" tab="主体信息">
          <BaseForm ref="BaseFormRef" v-model="companyForm" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicPopup>
</template>

<style></style>
