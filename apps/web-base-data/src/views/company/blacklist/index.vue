<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AccessInfo } from '#/api';

import { reactive, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getBlackListPageListApi, removeBlackListApi } from '#/api';
import BlacklistDetail from '#/views/company/blacklist/blacklist-detail.vue';
import BlacklistEdit from '#/views/company/blacklist/blacklist-edit.vue';

const dictStore = useDictStore();
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        options: dictStore.getDictList('BLACKLIST_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
  ],
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'companyName', title: '企业名称', minWidth: 200 },
    { field: 'companyCode', title: '统一社会信用代码' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BLACKLIST_STATUS',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getBlackListPageListApi({
          ...formValues,
          tabType: store.activeKey,
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: AccessInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: AccessInfo) => {
  openDetailPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const addBlacklist = () => {
  openFormPopup(true, {});
};
const removeBlacklistForm = ref({
  id: 0,
  companyName: '',
  companyCode: '',
  remark: '',
});
const cancel = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  removeBlacklistForm.value = {
    id: res[0].id,
    companyName: res[0].companyName,
    companyCode: res[0].companyCode,
    remark: '',
  };
  modalApi.open();
};
const RemoveBlacklistFormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await RemoveBlacklistFormRef.value.validate();
    await removeBlackListApi(removeBlacklistForm.value);
    await modalApi.close();
    await gridApi.reload();
  },
  onClosed: () => {
    RemoveBlacklistFormRef.value.resetFields();
    removeBlacklistForm.value = {
      id: 0,
      companyName: '',
      companyCode: '',
      remark: '',
    };
  },
});
const store = reactive({
  activeKey: '1',
});
const rules = {
  remark: [{ required: true, message: '请输入移除原因', trigger: 'change' }],
};
const changeTab = () => {
  gridApi.reload();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #form-append>
        <a-tabs v-model:active-key="store.activeKey" @change="changeTab">
          <a-tab-pane key="1" tab="黑名单" />
          <a-tab-pane key="2" tab="历史记录" />
        </a-tabs>
      </template>
      <template #toolbar-actions>
        <a-space v-if="store.activeKey === '1'">
          <a-button type="primary" @click="addBlacklist">新增</a-button>
          <a-button type="primary" danger @click="cancel">移除</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="viewDetail(row)">详情</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <BlacklistEdit @register="registerForm" @ok="editSuccess" />
    <BlacklistDetail @register="registerDetail" />
    <Modal title="移除黑名单">
      <a-form ref="RemoveBlacklistFormRef" :model="removeBlacklistForm" :rules="rules">
        <a-form-item label="企业名称" name="">
          <p>{{ removeBlacklistForm.companyName }}</p>
        </a-form-item>
        <a-form-item label="统一社会信用代码" name="">
          <p>{{ removeBlacklistForm.companyCode }}</p>
        </a-form-item>
        <a-form-item label="移除原因" name="remark">
          <a-textarea v-model:value="removeBlacklistForm.remark" :rows="4" class="w-full" />
        </a-form-item>
      </a-form>
    </Modal>
  </Page>
</template>

<style></style>
