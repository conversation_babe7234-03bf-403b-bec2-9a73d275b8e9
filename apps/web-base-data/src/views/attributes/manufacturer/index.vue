<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ManufacturerInfo } from '#/api';

import { ref, unref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addAttributeManufacturerApi,
  deleteAttributeManufacturerApi,
  disableAttributeManufacturerApi,
  enableAttributeManufacturerApi,
  getAttributeManufacturerPageList,
  updateAttributeManufacturerApi,
} from '#/api';

import ManufacturerForm from './components/manufacturer-form.vue';

const dictStore = useDictStore();
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'manufacturerCode',
      label: '厂家编码',
    },
    {
      component: 'Input',
      fieldName: 'manufacturerName',
      label: '厂家名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '厂家状态',
      componentProps: {
        options: dictStore.getDictList('baseEnableType'),
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'manufacturerCode', title: '生产厂家编码' },
    { field: 'manufacturerName', title: '生产厂家名称' },
    { field: 'factoryNumber', title: '厂商' },
    { field: 'remarks', title: '备注' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { manufacturerCode: string; manufacturerName: string; status: string },
      ) => {
        return await getAttributeManufacturerPageList({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const manufacturerFormRef = ref();
const modalTitle = ref('新增生产厂家');
const currentManufacturer = ref({});

const [Modal, modalApi] = useVbenModal({
  onOpened: async () => {
    manufacturerFormRef.value.init(currentManufacturer.value);
  },
  onConfirm: async () => {
    currentManufacturer.value = await manufacturerFormRef.value.submit();
    let api = addAttributeManufacturerApi;
    if (currentManufacturer.value.pid) {
      api = updateAttributeManufacturerApi;
    }
    await api(unref(currentManufacturer) as ManufacturerInfo);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
});

const addManufacturer = () => {
  modalTitle.value = '新增生产厂家';
  currentManufacturer.value = {};
  modalApi.open();
};

const editManufacturer = (row: ManufacturerInfo) => {
  modalTitle.value = '编辑生产厂家';
  currentManufacturer.value = { ...row };
  modalApi.open();
};
const enableRows = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.pid);
  AModal.confirm({
    title: '确认启用',
    content: '确认启用此生产厂家？',
    onOk: async () => {
      await enableAttributeManufacturerApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const disenableRows = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.pid);
  AModal.confirm({
    title: '确认禁用',
    content: '确认禁用此生产厂家？',
    onOk: async () => {
      await disableAttributeManufacturerApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const delRows = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.pid);
  AModal.confirm({
    title: '确认删除',
    content: '确认删除此生产厂家？',
    onOk: async () => {
      await deleteAttributeManufacturerApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="addManufacturer">新增</a-button>
        <a-button class="mr-2" type="primary" @click="enableRows">启用</a-button>
        <a-button class="mr-2" danger type="primary" @click="disenableRows">禁用</a-button>
        <a-button class="mr-2" danger type="primary" @click="delRows">删除</a-button>
      </template>
      <template #action="{ row }">
        <a-typography-link @click="editManufacturer(row)">编辑</a-typography-link>
      </template>
    </Grid>

    <Modal :title="modalTitle">
      <ManufacturerForm ref="manufacturerFormRef" />
    </Modal>
  </Page>
</template>

<style></style>
