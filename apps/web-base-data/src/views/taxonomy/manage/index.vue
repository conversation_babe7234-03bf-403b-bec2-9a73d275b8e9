<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { TaxonomyInfo } from '#/api';

import { ref, unref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addTaxonomyApi,
  deleteTaxonomyApi,
  disableTaxonomyApi,
  enableTaxonomyApi,
  getTaxonomyTreeListApi,
  saveAttributeCategoryApi,
  updateTaxonomyApi,
} from '#/api';
import AttributesForm from '#/views/taxonomy/manage/components/attributes-form.vue';
import BatchAttributeSettingModal from '#/views/taxonomy/manage/components/batch-attribute-setting-modal.vue';
import TaxonomyForm from '#/views/taxonomy/manage/components/taxonomy-form.vue';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'categoryCode',
      label: '商品分类编码',
    },
    {
      component: 'Input',
      fieldName: 'categoryName',
      label: '商品分类名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '商品分类状态',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  treeConfig: {
    rowField: 'id',
    childrenField: 'children',
  },
  checkboxConfig: {
    checkStrictly: true,
  },
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'categoryCode', title: '分类编码', treeNode: true, minWidth: 300 },
    { field: 'categoryName', title: '分类名称' },
    { field: 'spuQty', title: '商品数量' },
    // { field: 'attributeQty', title: '属性数量' },
    { field: 'sort', title: '排序' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    ajax: {
      query: async (_: never, formValues: { categoryCode: string; categoryName: string; status: string }) => {
        return await getTaxonomyTreeListApi({
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const clearTreeExpand = () => {
  gridApi.grid.clearTreeExpand();
};
const treeExpandAll = () => {
  gridApi.grid.setAllTreeExpand(true);
};
const modalTitle = ref('新增分类');
const loading = ref({
  save: false,
});
const taxonomyFormRef = ref();
const taxonomyForm = ref<Partial<TaxonomyInfo>>({});
const [Modal, modalApi] = useVbenModal({
  onOpened: async () => {
    taxonomyFormRef.value.init(taxonomyForm.value);
  },
  onConfirm: async () => {
    taxonomyForm.value = await taxonomyFormRef.value.submit();
    let api = addTaxonomyApi;
    if (taxonomyForm.value.id) {
      api = updateTaxonomyApi;
    }
    await api(unref(taxonomyForm) as TaxonomyInfo);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
});
const addTaxonomy = () => {
  taxonomyForm.value = {};
  modalTitle.value = '新增分类';
  modalApi.open();
};
const editTaxonomy = (row: TaxonomyInfo) => {
  taxonomyForm.value = row;
  modalTitle.value = '编辑分类';
  modalApi.open();
};
const attributesFormRef = ref();
const [AttributesModal, attributesModalApi] = useVbenModal({
  onOpened: async () => {
    attributesFormRef.value.init(attributesModalApi.getData());
  },
  onConfirm: async () => {
    const formData = await attributesFormRef.value.submit();
    await saveAttributeCategoryApi(formData);
    await attributesModalApi.close();
    await gridApi.reload();
    message.success($t('base.resSuccess'));
  },
});
// const editAttributes = (row: TaxonomyInfo) => {
//   attributesModalApi.setData(row).open();
// };
const [BatchAttrSettingModal] = useVbenModal({
  connectedComponent: BatchAttributeSettingModal,
});
// const batchAttributeSettings = () => {
//   const data = gridApi.grid.getCheckboxRecords(true);
//   batchAttrSettingModalApi.setData(data).open();
// };
const changeTaxonomyStatus = async (status: 0 | 1) => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.id);
  const operation = {
    1: {
      label: '启用',
      api: enableTaxonomyApi,
    },
    0: {
      label: '禁用',
      api: disableTaxonomyApi,
    },
  };
  AntdModal.confirm({
    title: `确认${operation[status].label}`,
    content: `确认${operation[status].label}此SKU吗？`,
    async onOk() {
      await operation[status].api(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const delTaxonomy = async () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.id);
  AntdModal.confirm({
    title: '确认删除',
    content: '确认删除此分类？',
    async onOk() {
      await deleteTaxonomyApi(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="addTaxonomy">新增分类</a-button>
          <a-button type="primary" @click="changeTaxonomyStatus(1)">启用</a-button>
          <a-button type="primary" danger @click="changeTaxonomyStatus(0)">禁用</a-button>
          <a-button type="primary" danger @click="delTaxonomy">删除</a-button>
          <!--<a-button type="primary" @click="batchAttributeSettings">批量属性设置</a-button>-->
        </a-space>
      </template>
      <template #toolbar-tools>
        <a-button class="mr-2" @click="treeExpandAll">展开所有</a-button>
        <a-button @click="clearTreeExpand">折叠所有</a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="editTaxonomy(row)">编辑</a-typography-link>
          <!--<a-typography-link @click="editAttributes(row)">属性设置</a-typography-link>-->
        </a-space>
      </template>
    </Grid>
    <Modal :title="modalTitle" :confirm-loading="loading.save">
      <TaxonomyForm ref="taxonomyFormRef" />
    </Modal>
    <AttributesModal title="属性设置" class="w-[1200px]">
      <AttributesForm ref="attributesFormRef" />
    </AttributesModal>
    <BatchAttrSettingModal />
  </Page>
</template>

<style></style>
