import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    name: 'Company',
    path: '/company',
    meta: {
      icon: 'lucide:layout-dashboard',
      title: 'page.company.title',
    },
    children: [
      {
        name: 'CompanyManage',
        path: '/company/manage',
        component: () => import('#/views/company/manage/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.company.manage',
        },
      },
      {
        name: 'CompanyAccess',
        path: '/company/access',
        component: () => import('#/views/company/access/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.company.access',
        },
      },
      {
        name: 'CompanyBlacklist',
        path: '/company/blacklist',
        component: () => import('#/views/company/blacklist/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.company.blacklist',
        },
      },
      {
        name: 'CompanyBusinessRole',
        path: '/company/business-role',
        component: () => import('#/views/company/business-role/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.company.businessRole',
        },
      },
    ],
  },
];
export default routes;
