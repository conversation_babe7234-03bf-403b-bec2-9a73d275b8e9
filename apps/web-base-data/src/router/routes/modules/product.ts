import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    name: 'Product',
    path: '/product',
    meta: {
      icon: 'lucide:layout-dashboard',
      title: 'page.product.title',
    },
    children: [
      {
        name: 'ProductMaterial',
        path: '/product/material',
        component: () => import('#/views/product/material/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.product.manage',
        },
      },
      {
        name: 'ProductManage',
        path: '/product/manage',
        component: () => import('#/views/product/manage/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.product.manage',
        },
      },
    ],
  },
];
export default routes;
