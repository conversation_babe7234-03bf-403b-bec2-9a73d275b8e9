import { faker<PERSON><PERSON>_CN as faker } from '@faker-js/faker';

function generateMockData() {
  return {
    pid: faker.string.uuid(),
    code: faker.string.uuid(),
    deliveryMethod: faker.helpers.arrayElement(['01', '02', '03']),
    invoiceMethod: faker.helpers.arrayElement(['01', '02', '03']),
    amount: faker.number.int({ max: 10_000_000, multipleOf: 10_000 }),
    name: faker.commerce.productName(),
    associatedOrderName: faker.commerce.productName(),
    associatedOrderNo: faker.string.uuid(),
    projectCode: faker.string.uuid(),
    projectName: faker.commerce.productName(),
    auditStatus: faker.helpers.arrayElement(['00', '10', '20']),
    reason: faker.lorem.lines(),
    remarks: faker.lorem.lines(),
    status: faker.helpers.arrayElement(['00', '10', '20', '30', '40']),
    mode: faker.helpers.arrayElement(['10', '20', '30']),
    currency: faker.helpers.arrayElement(['01']),
    tradeExecutionEnterpriseName: faker.company.name(),
    upEnterpriseName: faker.company.name(),
    downEnterpriseName: faker.company.name(),
    projectType: faker.helpers.arrayElement(['类型A', '类型B', '类型C']),
    businessDate: faker.date.past().toISOString(),
    createDepartment: faker.commerce.department(),
    createBy: faker.person.fullName(),
    createTime: faker.date.recent().toISOString(),
    estimateExchangeRate: faker.commerce.price({ min: 1, max: 9 }),
    planStartDate: faker.date.future().toISOString(), // 随机未来日期（计划开始日期）
    planDays: faker.number.int({ min: 1, max: 365 }), // 随机天数（计划天数）
    planCompletionDate: faker.date.future().toISOString(), // 随机未来日期（计划完成日期）
    estimatedPaymentDate: faker.date.future().toISOString(), // 随机未来日期（预计付款日期）
    estimatedInvoiceDate: faker.date.future().toISOString(), // 随机未来日期（预计开票日期）
    hasAdvancePayment: faker.datatype.boolean() ? 1 : 0, // 是否有预付款（0 或 1）
    advancePaymentPercentage: faker.finance.amount({
      min: 0,
      max: 100,
    }),
    advancePaymentAmount: faker.commerce.price({
      min: 0,
      max: 10_000,
    }), // 随机金额（预付款金额）
    goodsList: Array.from(
      { length: faker.number.int({ min: 1, max: 5 }) },
      () => ({
        goodsName: faker.commerce.productName(),
        goodsType: faker.commerce.productName(),
        sku: faker.commerce.productName(),
        unit: faker.helpers.arrayElement(['吨']),
        models: faker.helpers.arrayElement([
          '规格A',
          '规格B',
          '规格C',
          '规格D',
        ]),
        taxRate: faker.helpers.arrayElement([6, 9, 13]),
        num: faker.number.int({ min: 1, max: 100 }),
        unitPriceTax: faker.commerce.price({ min: 10, max: 1000 }),
        get amountTax() {
          return (this.num * this.unitPriceTax).toFixed(2);
        },
        remark: faker.lorem.sentence(5),
        brand: faker.company.name(), // 品牌名称
        factory: faker.company.name(), // 工厂名称
        currency: faker.finance.currencyCode(), // 货币代码（如 USD, EUR）
        estimateExchangeRate: faker.commerce.price({ min: 1, max: 9 }),
        unitPrice: faker.commerce.price({ min: 10, max: 1000 }),
        amount: faker.commerce.price({ min: 10, max: 1000 }),
        tax: faker.commerce.price({ min: 10, max: 1000 }),
      }),
    ),
  };
}
const mockData = generateMockData();

export default eventHandler(async () => {
  return useResponseSuccess(mockData);
});
